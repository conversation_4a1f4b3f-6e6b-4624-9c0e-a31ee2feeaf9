{"success": true, "message": "Success Get Detail Transaction", "data": {"id": 17405, "createdAt": "2025-04-08T16:59:28", "updatedAt": "2025-04-08T17:00:53", "createdBy": "3041347", "updatedBy": "3021586", "currentLayerState": null, "data": {"oldData": "[{\"effectiveEndDate\":\"4712-12-31\",\"country\":\"MY\",\"rt\":\"005\",\"townOrCity\":\"Kepong\",\"lastModifiedDate\":\"2020-11-26 10:43:54\",\"rw\":null,\"addressType\":\"CIMB_KTP\",\"lastModifiedBy\":\"FUSION_APPS_HCM_ESS_LOADER_APPID\",\"postalCode\":\"52100\",\"active\":true,\"kelurahan\":\"WAY KANDIS\",\"addressId\":300001003701916,\"createdDate\":\"2020-11-25 05:46:45\",\"province\":null,\"createdBy\":\"FUSION_APPS_HCM_ESS_LOADER_APPID\",\"empConvStatus\":null,\"effectiveStartDate\":\"2020-06-02\",\"kecamatan\":\"TANJUNG SENANG\",\"addressLine1\":\"Station\",\"addressLine2\":\"Road\",\"personId\":100000404571707,\"id\":6724,\"addressLine3\":null,\"dateTrackUpdateMode\":null}]", "newData": "[{\"effectiveEndDate\":\"4712-12-31\",\"country\":\"Indonesia\",\"rt\":\"001\",\"rw\":\"002\",\"postalCode\":\"16517\",\"addressId\":300001003701916,\"province\":\"Bali\",\"effectiveStartDate\":\"2020-06-02\",\"addressLine1\":\"ROAD 1\",\"action\":\"edit\",\"addressLine2\":\"ROAD 2\",\"id\":46735,\"addressLine3\":null,\"dateTrackUpdateMode\":null,\"townOrCity\":\"Badung\",\"lastModifiedDate\":\"2020-11-26 10:43:54\",\"addressType\":\"CIMB_KTP\",\"lastModifiedBy\":\"FUSION_APPS_HCM_ESS_LOADER_APPID\",\"active\":false,\"kelurahan\":\"SERUA\",\"createdDate\":\"2020-11-25 05:46:45\",\"createdBy\":\"FUSION_APPS_HCM_ESS_LOADER_APPID\",\"empConvStatus\":null,\"kecamatan\":\"BOJONGSARI\",\"personId\":100000404571707}]", "message": null}, "expiryDate": "2025-04-08T17:00:53", "taskName": "EMPLOYEE_APPROVAL_1744106367719_3041347", "layers": [{"id": 48214, "createdAt": "2025-04-08 16:59:28", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "minApprover": 1, "value": 0.0, "code": "layer-2", "name": "layer-2", "previous": 48213, "next": null, "duration": 100, "approvers": [{"id": 186373, "approverCode": "3020365", "approverNik": "3020365", "approverName": "NASRUN", "approverJob": {"id": 16525, "fsJobId": 300002459950393, "jobCode": "ID514126125720", "jobTitle": "Onboarding, Personnel & Benefit Services Head", "jobFamily": "Human Resources", "dateFrom": null, "jobCategory": "ENABLER", "jobLevel": "L2", "functionCode": "HR Service Center", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 4032, "fsPositionId": 300002948435931, "positionCode": "84769", "positionTitle": "Onboarding, Personnel & Benefit Services Head O622", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D2", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 96286, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O622 Onboarding, Personnel & Benefit Services ID", "type": "L2", "dateFrom": "2022-06-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186364, "approverCode": "3033690", "approverNik": "3033690", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 10742, "fsJobId": 30*************, "jobCode": "ID255136", "jobTitle": "Head of Loan Workout", "jobFamily": "Management", "dateFrom": "1951-01-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L1", "functionCode": "Division Head", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 1392, "fsPositionId": ***************, "positionCode": "88902", "positionTitle": "Head of Loan Workout - Corporate Banking O822", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D1", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4918, "fsLocationId": ***************, "locationCode": "IDO522429", "locationName": "O522 Kantor Fungsional Non Operasional - Menara Sudirman", "description": "O522 Kantor Fungsional Non Operasional - Menara Sudirman", "city": "KERTEH", "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 97387, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O822 Loan Workout - Corporate Banking ID", "type": "L1", "dateFrom": "2022-08-01", "romDivision": "Group Risk - Special Asset"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186386, "approverCode": "3035475", "approverNik": "3035475", "approverName": "<PERSON><PERSON><PERSON>", "approverJob": {"id": 10619, "fsJobId": ***************, "jobCode": "ID254833", "jobTitle": "Inspection & Valuation Manager", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L4", "functionCode": "Credit Scoring", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 50571, "fsPositionId": ***************, "positionCode": "95299", "positionTitle": "Inspection & Valuation Manager - Central 1 1222", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D5", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 100356, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - Central 1 D4 ID", "type": "L4", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186375, "approverCode": "5381470", "approverNik": "5381470", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 19086, "fsJobId": ***************, "jobCode": "ID4240880861135", "jobTitle": "Sr Business Analyst", "jobFamily": "Digital Project Management", "dateFrom": "2022-10-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "IT Project Management", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49546, "fsPositionId": ***************, "positionCode": "90941", "positionTitle": "Sr Business Analyst - RM, Compliance, Corporate Affair, Legal, Audit & HR 1022", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "4", "headCount": "4", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 98353, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1022 Business Analyst - RM, Compliance, Corporate Affair, Legal, Audit & HR D4 ID", "type": "L4", "dateFrom": "2022-10-01", "romDivision": "Group Technology"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186369, "approverCode": "3021586", "approverNik": "3021586", "approverName": "ADNAN RUSDIAN", "approverJob": {"id": 16528, "fsJobId": 300002459950420, "jobCode": "ID514126125722", "jobTitle": "TA Operations & Onboarding Officer", "jobFamily": "Human Resources", "dateFrom": "2022-02-01", "jobCategory": "ENABLER", "jobLevel": "L4 - IC", "functionCode": "HR Service Center", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 46894, "fsPositionId": 300002948435943, "positionCode": "84767", "positionTitle": "TA Operations & Onboarding Officer O622", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "3", "headCount": "3", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 96306, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O622 TA Operations & Onboarding D4 ID", "type": "L4", "dateFrom": "2022-06-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "3021586", "status": {"id": 34, "code": "approve", "description": "Approved", "type": "approver"}, "reason": "adfsf", "isAdmin": null, "attachments": []}, {"id": 186358, "approverCode": "3026445", "approverNik": "3026445", "approverName": "NAKAMI ASAKU", "approverJob": {"id": 2429, "fsJobId": 300000939022913, "jobCode": "ID152855", "jobTitle": "Human Resources Director", "jobFamily": "Business Management", "dateFrom": "1951-01-01", "jobCategory": "Business Development & Product", "jobLevel": "L0", "functionCode": "Senior Management", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 826, "fsPositionId": 300003207818067, "positionCode": "90951", "positionTitle": "Human Resources Director O922", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D0", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": null, "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4889, "fsLocationId": 300002788523786, "locationCode": "IDO522431", "locationName": "O522 Kantor P<PERSON>t - Graha CIMB Niaga", "description": "O522 Kantor P<PERSON>t - Graha CIMB Niaga", "city": "JAKARTA SELATAN", "province": "DKI JAKARTA", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 98416, "fsDepartmentId": 300003207509380, "departmentCode": "300003207509380", "departmentName": "O922 Human Resources ID", "type": "L0", "dateFrom": "2022-09-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186384, "approverCode": "3003846", "approverNik": "3003846", "approverName": "<PERSON><PERSON><PERSON>", "approverJob": {"id": 6516, "fsJobId": 300000939058224, "jobCode": "ID246679", "jobTitle": "Loan Disbursement & Maintenance Officer", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L5 - IC", "functionCode": "Credit & Loan Management Services", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 107275, "fsPositionId": 300003418861364, "positionCode": "95287", "positionTitle": "Loan Disbursement & Maintenance Officer - East - DRO 1222", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "4", "headCount": "4", "reportingLine": "D6", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4814, "fsLocationId": 300002788521889, "locationCode": "IDO522089", "locationName": "O522 KC - Surabaya - Darmo - DRO", "description": "O522 KC - Surabaya - Darmo - DRO", "city": "<PERSON>", "province": null, "country": "Malaysia", "initialBranch": "DRO", "branchCode": "013", "branchSize": "Extra Large", "branchType": "KC"}, "approverDepartment": {"id": 100363, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Loan Disbursement & Maintenance - East D5 ID", "type": "L5", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186371, "approverCode": "3016316", "approverNik": "3016316", "approverName": "<PERSON><PERSON><PERSON>", "approverJob": {"id": 16520, "fsJobId": ***************, "jobCode": "ID415057056736", "jobTitle": "HR Information System Officer", "jobFamily": "Digital Development", "dateFrom": "2022-02-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "HR Information Systems", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 51812, "fsPositionId": ***************, "positionCode": "99219", "positionTitle": "HR Information System Officer O423", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "5", "headCount": "5", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 101654, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O423 HR Information System ID", "type": "L4", "dateFrom": "2023-05-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186361, "approverCode": "3010748", "approverNik": "3010748", "approverName": "3010748", "approverJob": null, "approverPosition": null, "approverLocation": null, "approverDepartment": null, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186393, "approverCode": "3019936", "approverNik": "3019936", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 19169, "fsJobId": 300003285572549, "jobCode": "ID10222228", "jobTitle": "Card, Lending & Collection Incentive Management Head", "jobFamily": "Data Analytics", "dateFrom": "2022-10-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L3", "functionCode": "Data Analyst", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 20531, "fsPositionId": 300003285570709, "positionCode": "91467", "positionTitle": "Card, Lending & Collection Incentive Management Head 1022", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D3", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5033, "fsLocationId": 300002788525531, "locationCode": "IDO522426", "locationName": "O522 Kantor Fungsional Non Operasional - Synergy Building", "description": "O522 Kantor Fungsional Non Operasional - Synergy Building", "city": "Banting", "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 98593, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1022 Card, Lending & Collection Incentive Management D3 ID", "type": "L3", "dateFrom": "2022-10-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186387, "approverCode": "3005146", "approverNik": "3005146", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 9580, "fsJobId": ***************, "jobCode": "ID253665", "jobTitle": "Loan Disbursement & Maintenance Manager", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L4", "functionCode": "Credit & Loan Management Services", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 444760, "fsPositionId": *****************, "positionCode": "IDP1100010", "positionTitle": "Application Developer Staff Squad 3", "type": "SINGLE", "hiringStatus": "Approved", "fte": "1", "headCount": "1", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": null, "highriskPosition": null, "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4814, "fsLocationId": 300002788521889, "locationCode": "IDO522089", "locationName": "O522 KC - Surabaya - Darmo - DRO", "description": "O522 KC - Surabaya - Darmo - DRO", "city": "<PERSON>", "province": null, "country": "Malaysia", "initialBranch": "DRO", "branchCode": "013", "branchSize": "Extra Large", "branchType": "KC"}, "approverDepartment": {"id": 100338, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Loan Disbursement & Maintenance - East D4 ID", "type": "L4", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186362, "approverCode": "5397644", "approverNik": "5397644", "approverName": "Hoo ", "approverJob": {"id": 19075, "fsJobId": ***************, "jobCode": "ID09222160", "jobTitle": "Personnel Services Officer", "jobFamily": "Human Resources", "dateFrom": "2022-09-01", "jobCategory": "ENABLER", "jobLevel": "L4 - IC", "functionCode": "HR Service Center", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49447, "fsPositionId": ***************, "positionCode": "90731", "positionTitle": "Personnel Services & Organization Maintenance Officer - PS O922", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "2", "headCount": "2", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 96309, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance D4 ID - Edited", "type": "L4", "dateFrom": "2022-06-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186383, "approverCode": "3009279", "approverNik": "3009279", "approverName": "Subamaniam", "approverJob": {"id": 10444, "fsJobId": 300000939075075, "jobCode": "ID254119", "jobTitle": "Loan Disbursement & Maintenance Sr Officer", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L5 - IC", "functionCode": "Credit & Loan Management Services", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 107274, "fsPositionId": 300003418861363, "positionCode": "95265", "positionTitle": "Sr Loan Disbursement & Maintenance Officer - East - DRO 1222", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D6", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4814, "fsLocationId": 300002788521889, "locationCode": "IDO522089", "locationName": "O522 KC - Surabaya - Darmo - DRO", "description": "O522 KC - Surabaya - Darmo - DRO", "city": "<PERSON>", "province": null, "country": "Malaysia", "initialBranch": "DRO", "branchCode": "013", "branchSize": "Extra Large", "branchType": "KC"}, "approverDepartment": {"id": 100363, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Loan Disbursement & Maintenance - East D5 ID", "type": "L5", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186392, "approverCode": "539744", "approverNik": "539744", "approverName": "539744", "approverJob": null, "approverPosition": null, "approverLocation": null, "approverDepartment": null, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186380, "approverCode": "5378968", "approverNik": "5378968", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 15703, "fsJobId": ***************, "jobCode": "3D-1_AFT010_00015", "jobTitle": "Card, Lending & Collection Incentive Management Analyst", "jobFamily": "Data Analytics", "dateFrom": "2020-12-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "Data Analyst", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49826, "fsPositionId": 300003285570710, "positionCode": "91452", "positionTitle": "Card, Lending & Collection Incentive Management Analyst 1022", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "4", "headCount": "4", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5033, "fsLocationId": 300002788525531, "locationCode": "IDO522426", "locationName": "O522 Kantor Fungsional Non Operasional - Synergy Building", "description": "O522 Kantor Fungsional Non Operasional - Synergy Building", "city": "Banting", "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 98580, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1022 Card, Lending & Collection Incentive Management D4 ID", "type": "L4", "dateFrom": "2022-10-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186394, "approverCode": "dion", "approverNik": "dion", "approverName": "dion", "approverJob": null, "approverPosition": null, "approverLocation": null, "approverDepartment": null, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186359, "approverCode": "3024157", "approverNik": "3024157", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 19495, "fsJobId": ***************, "jobCode": "ID04230207", "jobTitle": "Property Management Head", "jobFamily": "General Administration & Secretarial", "dateFrom": "2023-04-01", "jobCategory": "OPERATIONS & ADMINISTRATION", "jobLevel": "L3", "functionCode": "Facilities Management", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": null, "approverLocation": {"id": 5043, "fsLocationId": 300002788525652, "locationCode": "IDO522428", "locationName": "O522 Kantor Fungsional Non Operasional - SPAPM - Jakarta", "description": "O522 Kantor Fungsional Non Operasional - SPAPM - Jakarta", "city": null, "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 101579, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O423 Property Management D3 ID", "type": "L3", "dateFrom": "2023-04-01", "romDivision": "Group Strategic Procurement"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186376, "approverCode": "3039515", "approverNik": "3039515", "approverName": "<PERSON>", "approverJob": {"id": 16280, "fsJobId": 300001989045815, "jobCode": "ID514126125503", "jobTitle": "Personnel Services & Organization Maintenance Head-<PERSON> <PERSON><PERSON><PERSON>", "jobFamily": "Human Resources", "dateFrom": null, "jobCategory": "ENABLER", "jobLevel": "L3", "functionCode": "HR Service Center", "jobClassification": "Bonus Based", "jobField": "000", "jobDescription1": "<p>test test test test test test</p>", "jobDescription2": null, "jobCertification": "Y", "uniqueJob": "Specific"}, "approverPosition": {"id": 49343, "fsPositionId": ***************, "positionCode": "90310", "positionTitle": "Building Maintenance &  Project Officer O822", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "6", "headCount": "6", "reportingLine": "D5", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 96282, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance ID", "type": "L3", "dateFrom": "2022-06-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186360, "approverCode": "3026431", "approverNik": "3026431", "approverName": "SYAQIF CONSTANTIN ARSALAN", "approverJob": {"id": 19029, "fsJobId": 300003129742325, "jobCode": "ID5291321311114", "jobTitle": "Strategic Sourcing Head", "jobFamily": "Procurement", "dateFrom": "2022-08-01", "jobCategory": "ENABLER", "jobLevel": "L3", "functionCode": "Procurement", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 21133, "fsPositionId": 300003516122026, "positionCode": "96369", "positionTitle": "Campaign Analytics Specialist O123", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D3", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5043, "fsLocationId": 300002788525652, "locationCode": "IDO522428", "locationName": "O522 Kantor Fungsional Non Operasional - SPAPM - Jakarta", "description": "O522 Kantor Fungsional Non Operasional - SPAPM - Jakarta", "city": null, "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 97789, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O822 Strategic Sourcing - Non IT D3 ID", "type": "L3", "dateFrom": "2022-08-01", "romDivision": "Group Strategic Procurement"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186368, "approverCode": "5321125", "approverNik": "5321125", "approverName": "<PERSON>", "approverJob": {"id": 19075, "fsJobId": ***************, "jobCode": "ID09222160", "jobTitle": "Personnel Services Officer", "jobFamily": "Human Resources", "dateFrom": "2022-09-01", "jobCategory": "ENABLER", "jobLevel": "L4 - IC", "functionCode": "HR Service Center", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49447, "fsPositionId": ***************, "positionCode": "90731", "positionTitle": "Personnel Services & Organization Maintenance Officer - PS O922", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "2", "headCount": "2", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 96309, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance D4 ID - Edited", "type": "L4", "dateFrom": "2022-06-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186389, "approverCode": "5391353", "approverNik": "5391353", "approverName": "ADENONI AYU TAMPUBOLON", "approverJob": {"id": 3366, "fsJobId": 300000939029728, "jobCode": "ID243158", "jobTitle": "Card Collection Staff", "jobFamily": "Credit Collection", "dateFrom": "1951-01-01", "jobCategory": "COLLECTION & RECOVERY", "jobLevel": "L6 - IC", "functionCode": "Consumer Collection", "jobClassification": "Incentive Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 162231, "fsPositionId": 300003864455754, "positionCode": "101395", "positionTitle": "Card Collection Staff - BE - Field - Jabodetabek - 3 O523", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "4", "headCount": "4", "reportingLine": "D6", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4878, "fsLocationId": 300002788523656, "locationCode": "IDO522425", "locationName": "O522 Kantor Fungsional Non Operasional - Menara Sentraya", "description": "O522 Kantor Fungsional Non Operasional - Menara Sentraya", "city": "Kuala Lumpur", "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": "003", "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 102168, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O523 Card Collection - BE - Field - Jabodetabek - 3 D6 ID", "type": "L6", "dateFrom": "2023-05-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186395, "approverCode": "5416666", "approverNik": "5416666", "approverName": "5416666", "approverJob": null, "approverPosition": null, "approverLocation": null, "approverDepartment": null, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186377, "approverCode": "3032314", "approverNik": "3032314", "approverName": "DIETER", "approverJob": {"id": 7303, "fsJobId": ***************, "jobCode": "ID251554", "jobTitle": "<PERSON>an Workout Head", "jobFamily": "Credit Collection", "dateFrom": "1951-01-01", "jobCategory": "COLLECTION & RECOVERY", "jobLevel": "L2", "functionCode": "Loan Workout", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 4128, "fsPositionId": ***************, "positionCode": "88897", "positionTitle": "Loan Workout Head - Corporate Banking II O822", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D2", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4918, "fsLocationId": ***************, "locationCode": "IDO522429", "locationName": "O522 Kantor Fungsional Non Operasional - Menara Sudirman", "description": "O522 Kantor Fungsional Non Operasional - Menara Sudirman", "city": "KERTEH", "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 97393, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O822 Loan Workout - Corporate Banking II ID", "type": "L2", "dateFrom": "2022-08-01", "romDivision": "Group Risk - Special Asset"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186363, "approverCode": "qasq3", "approverNik": "qasq3", "approverName": "qasq3", "approverJob": null, "approverPosition": null, "approverLocation": null, "approverDepartment": null, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186370, "approverCode": "3038205", "approverNik": "3038205", "approverName": "<PERSON>n ", "approverJob": {"id": 13010, "fsJobId": ***************, "jobCode": "ID292501", "jobTitle": "Squad Lead", "jobFamily": "Digital Development", "dateFrom": "1951-01-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "Application Development Generalist", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49620, "fsPositionId": 300003207703298, "positionCode": "90741", "positionTitle": "Squad Lead - Enablers Squad 1022", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 98338, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1022 Enablers Squad ID", "type": "L4", "dateFrom": "2022-10-01", "romDivision": "Group Technology"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186391, "approverCode": "5408010", "approverNik": "5408010", "approverName": "<PERSON><PERSON><PERSON>", "approverJob": {"id": 3366, "fsJobId": 300000939029728, "jobCode": "ID243158", "jobTitle": "Card Collection Staff", "jobFamily": "Credit Collection", "dateFrom": "1951-01-01", "jobCategory": "COLLECTION & RECOVERY", "jobLevel": "L6 - IC", "functionCode": "Consumer Collection", "jobClassification": "Incentive Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 162378, "fsPositionId": 300004084052300, "positionCode": "103506", "positionTitle": "Card Collection Staff II - X-Days Jabodetabek I - Griya O823", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "10", "headCount": "10", "reportingLine": "D6", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 102904, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O823 Card Collection II - X-Days Jabodetabek I D6 ID", "type": "L6", "dateFrom": "2023-08-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186385, "approverCode": "3037007", "approverNik": "3037007", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 10651, "fsJobId": ***************, "jobCode": "ID254834", "jobTitle": "Inspection & Valuation Officer", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L5 - IC", "functionCode": "Credit Scoring", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 107279, "fsPositionId": ***************, "positionCode": "95231", "positionTitle": "Inspection & Valuation Officer - East 1 - RYD 1222", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D6", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4809, "fsLocationId": 300002788521829, "locationCode": "IDO522088", "locationName": "O522 KC - <PERSON> - <PERSON><PERSON> 8 - RYD", "description": "O522 KC - <PERSON> - <PERSON><PERSON> 8 - RYD", "city": "KLATEN", "province": "JAWA TENGAH", "country": "Indonesia", "initialBranch": "RYD", "branchCode": "056", "branchSize": "Large", "branchType": "KC"}, "approverDepartment": {"id": 100318, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - East 1 D5 ID", "type": "L5", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186378, "approverCode": "3003937", "approverNik": "3003937", "approverName": "MOHD NAZOR", "approverJob": {"id": 16614, "fsJobId": ***************, "jobCode": "ID408035035796", "jobTitle": "Network Region Service & Operations Head", "jobFamily": "Management", "dateFrom": "2022-04-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L2", "functionCode": "Division Head", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 4573, "fsPositionId": ***************, "positionCode": "98672", "positionTitle": "Network Region Service & Operations Head - Sumatera Region O423", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D2", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4750, "fsLocationId": 300002788469114, "locationCode": "IDO522075", "locationName": "O522 KC - Medan - Bukit Barisan - BKT", "description": "O522 KC - Medan - Bukit Barisan - BKT", "city": null, "province": null, "country": "Malaysia", "initialBranch": "BKT", "branchCode": "027", "branchSize": "Large", "branchType": "KC"}, "approverDepartment": {"id": 101361, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O423 Network Region Service & Operations - Sumatera Region ID", "type": "L2", "dateFrom": "2023-04-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186382, "approverCode": "3035474", "approverNik": "3035474", "approverName": "<PERSON>", "approverJob": {"id": 10651, "fsJobId": ***************, "jobCode": "ID254834", "jobTitle": "Inspection & Valuation Officer", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L5 - IC", "functionCode": "Credit Scoring", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 107301, "fsPositionId": ***************, "positionCode": "95234", "positionTitle": "Inspection & Valuation Officer - Central 1 - BTO 1222", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "3", "headCount": "3", "reportingLine": "D6", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 100321, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - Central 1 D5 ID", "type": "L5", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186365, "approverCode": "3034500", "approverNik": "3034500", "approverName": "YOHANA", "approverJob": {"id": 732, "fsJobId": ***************, "jobCode": "ID168980", "jobTitle": "Head of Corporate Banking", "jobFamily": "Sales", "dateFrom": "1951-01-01", "jobCategory": "REVENUE GENERATOR", "jobLevel": "L1", "functionCode": "Relationship Management - Large Corporates/ Institutions", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 4127, "fsPositionId": ***************, "positionCode": "88900", "positionTitle": "Loan Workout Head - Corporate Banking I O822", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D2", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4918, "fsLocationId": ***************, "locationCode": "IDO522429", "locationName": "O522 Kantor Fungsional Non Operasional - Menara Sudirman", "description": "O522 Kantor Fungsional Non Operasional - Menara Sudirman", "city": "KERTEH", "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 97386, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O822 Loan Workout - Corporate Banking I ID", "type": "L2", "dateFrom": "2022-08-01", "romDivision": "Group Risk - Special Asset"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186381, "approverCode": "3035472", "approverNik": "3035472", "approverName": "I MADE WELI MOKSAOKA", "approverJob": {"id": 10651, "fsJobId": ***************, "jobCode": "ID254834", "jobTitle": "Inspection & Valuation Officer", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L5 - IC", "functionCode": "Credit Scoring", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 107301, "fsPositionId": ***************, "positionCode": "95234", "positionTitle": "Inspection & Valuation Officer - Central 1 - BTO 1222", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "3", "headCount": "3", "reportingLine": "D6", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 100321, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - Central 1 D5 ID", "type": "L5", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186374, "approverCode": "12790", "approverNik": "12790", "approverName": "PUJAKESUMA", "approverJob": {"id": 20120, "fsJobId": ***************, "jobCode": "ID04240826", "jobTitle": "General Admin & GHG Management Head", "jobFamily": "General Administration & Secretarial", "dateFrom": "2024-04-01", "jobCategory": "OPERATIONS & ADMINISTRATION", "jobLevel": "L2", "functionCode": "Workforce Analytics", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49343, "fsPositionId": ***************, "positionCode": "90310", "positionTitle": "Building Maintenance &  Project Officer O822", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "6", "headCount": "6", "reportingLine": "D5", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5043, "fsLocationId": 300002788525652, "locationCode": "IDO522428", "locationName": "O522 Kantor Fungsional Non Operasional - SPAPM - Jakarta", "description": "O522 Kantor Fungsional Non Operasional - SPAPM - Jakarta", "city": null, "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 176496, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O424 General Admin & GHG Management ID", "type": "L2", "dateFrom": "2024-04-01", "romDivision": "Group Strategic Procurement"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186366, "approverCode": "5321468", "approverNik": "5321468", "approverName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "approverJob": {"id": 19075, "fsJobId": ***************, "jobCode": "ID09222160", "jobTitle": "Personnel Services Officer", "jobFamily": "Human Resources", "dateFrom": "2022-09-01", "jobCategory": "ENABLER", "jobLevel": "L4 - IC", "functionCode": "HR Service Center", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49447, "fsPositionId": ***************, "positionCode": "90731", "positionTitle": "Personnel Services & Organization Maintenance Officer - PS O922", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "2", "headCount": "2", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 96309, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance D4 ID - Edited", "type": "L4", "dateFrom": "2022-06-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186390, "approverCode": "3027038", "approverNik": "3027038", "approverName": "<PERSON><PERSON><PERSON>", "approverJob": {"id": 10983, "fsJobId": 300000939080697, "jobCode": "ID255756", "jobTitle": "Unsecured Collection Team Leader", "jobFamily": "Credit Collection", "dateFrom": "1951-01-01", "jobCategory": "COLLECTION & RECOVERY", "jobLevel": "L5", "functionCode": "Consumer Collection", "jobClassification": "Incentive Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 109342, "fsPositionId": 300003864455753, "positionCode": "101405", "positionTitle": "Unsecured Collection Team Leader - BE - Field CC - Jabodetabek - 3 O523", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D5", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4878, "fsLocationId": 300002788523656, "locationCode": "IDO522425", "locationName": "O522 Kantor Fungsional Non Operasional - Menara Sentraya", "description": "O522 Kantor Fungsional Non Operasional - Menara Sentraya", "city": "Kuala Lumpur", "province": null, "country": "Malaysia", "initialBranch": "KFNO", "branchCode": "003", "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 102178, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O523 Unsecured Collection - BE - Field CC - Jabodetabek - 3 D5 ID", "type": "L5", "dateFrom": "2023-05-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186367, "approverCode": "3039525", "approverNik": "3039525", "approverName": "En ", "approverJob": {"id": 19223, "fsJobId": ***************, "jobCode": "ID11222288", "jobTitle": "Credit PreSigning Verification Specialist", "jobFamily": "Credit", "dateFrom": "2022-11-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L4 - IC", "functionCode": "Credit & Loan Management Services", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49996, "fsPositionId": ***************, "positionCode": "92162", "positionTitle": "Credit PreSigning Verification Specialist 1122", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "6", "headCount": "6", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Non Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 98853, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1122 Credit PreSigning Verification D4 ID", "type": "L4", "dateFrom": "2022-11-01", "romDivision": "Group Operations"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186379, "approverCode": "admin<PERSON><PERSON>", "approverNik": "admin<PERSON><PERSON>", "approverName": "admin<PERSON><PERSON>", "approverJob": null, "approverPosition": null, "approverLocation": null, "approverDepartment": null, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186372, "approverCode": "5346411", "approverNik": "5346411", "approverName": "<PERSON><PERSON>", "approverJob": {"id": 16520, "fsJobId": ***************, "jobCode": "ID415057056736", "jobTitle": "HR Information System Officer", "jobFamily": "Digital Development", "dateFrom": "2022-02-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "HR Information Systems", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 51812, "fsPositionId": ***************, "positionCode": "99219", "positionTitle": "HR Information System Officer O423", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "5", "headCount": "5", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 5159, "fsLocationId": 300002948095235, "locationCode": "IDO622442", "locationName": "O622 Kantor Fungsional Non Operasional - Pondok Indah Icon", "description": null, "city": "Jakarta Selatan", "province": "DKI Jakarta", "country": "Malaysia", "initialBranch": "KFNO", "branchCode": null, "branchSize": null, "branchType": "KFNO"}, "approverDepartment": {"id": 101654, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "O423 HR Information System ID", "type": "L4", "dateFrom": "2023-05-01", "romDivision": "Group Human Resource"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}, {"id": 186388, "approverCode": "3036344", "approverNik": "3036344", "approverName": "<PERSON>", "approverJob": {"id": 10619, "fsJobId": ***************, "jobCode": "ID254833", "jobTitle": "Inspection & Valuation Manager", "jobFamily": "Credit", "dateFrom": "1951-01-01", "jobCategory": "MONITORING & CONTROL", "jobLevel": "L4", "functionCode": "Credit Scoring", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 50566, "fsPositionId": 300003418861366, "positionCode": "95230", "positionTitle": "Inspection & Valuation Manager - East 1 1222", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "1", "headCount": "1", "reportingLine": "D5", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4831, "fsLocationId": 300002788522100, "locationCode": "IDO522084", "locationName": "O522 KC - Pontianak - Tanjungpura - PON", "description": "O522 KC - Pontianak - Tanjungpura - PON", "city": "MEDAN", "province": "SUMATERA UTARA", "country": "Indonesia", "initialBranch": "PON", "branchCode": "388", "branchSize": "Medium", "branchType": "KC"}, "approverDepartment": {"id": 100354, "fsDepartmentId": ***************, "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - East 1 D4 ID", "type": "L4", "dateFrom": "2022-12-01", "romDivision": "Group Consumer Banking"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:30", "updatedAt": "2025-04-08 17:00:53", "createdBy": "devx", "updatedBy": "devx", "status": null, "reason": null, "isAdmin": null, "attachments": []}], "status": {"id": 9, "code": "complete", "description": "Layer Is Complete", "type": "layer"}}, {"id": 48213, "createdAt": "2025-04-08 16:59:28", "updatedAt": "2025-04-08 16:59:30", "createdBy": "devx", "updatedBy": "devx", "minApprover": 1, "value": 0.0, "code": "3041347", "name": "3041347", "previous": null, "next": 48214, "duration": 100, "approvers": [{"id": 186357, "approverCode": "3041347", "approverNik": "3041347", "approverName": "NOR'AZAM AZAM", "approverJob": {"id": 12022, "fsJobId": ***************, "jobCode": "ID256542", "jobTitle": "Developer", "jobFamily": "Digital Development", "dateFrom": "1951-01-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "Application Development", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "approverPosition": {"id": 49618, "fsPositionId": 300003207703296, "positionCode": "90754", "positionTitle": "Developer - EBB Squad 1022", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "3", "headCount": "3", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "approverLocation": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "approverDepartment": {"id": 98294, "fsDepartmentId": 300003207320718, "departmentCode": "300003207320718", "departmentName": "1022 EBB Squad ID", "type": "L4", "dateFrom": "2022-10-01", "romDivision": "Group Technology"}, "approverDetail": "EMPTY", "approvalState": true, "counterApproval": null, "createdAt": "2025-04-08 16:59:28", "updatedAt": "2025-04-08 16:59:30", "createdBy": "devx", "updatedBy": "3041347", "status": {"id": 25, "code": "submit", "description": "Submitted", "type": "approver"}, "reason": null, "isAdmin": null, "attachments": []}], "status": {"id": 9, "code": "complete", "description": "Layer Is Complete", "type": "layer"}}], "status": "Task Is In Completed", "statusCode": "complete", "sendBackTo": null, "workflows": [{"createdAt": "2025-04-08T17:00:53", "updatedAt": "2025-04-08T17:00:53", "createdBy": "3021586", "updatedBy": "3021586", "id": 48532, "approverCode": null, "employeeNik": null, "employeeName": null, "jobCategory": null, "jobTitle": null, "jobLevel": null, "jobFamily": null, "positionCode": null, "positionTitle": null, "departmentCode": null, "departmentName": null, "action": "Completed", "counterApproval": 0, "reason": null, "description": "Completed", "note": null, "isCurrent": true, "isRequestor": false, "attachments": [], "approvers": []}, {"createdAt": "2025-04-08T17:00:53", "updatedAt": "2025-04-08T17:00:53", "createdBy": "3021586", "updatedBy": "3021586", "id": 48531, "approverCode": "3021586", "employeeNik": "3021586", "employeeName": "ADNAN RUSDIAN", "jobCategory": "ENABLER", "jobTitle": "TA Operations & Onboarding Officer", "jobLevel": "L4 - IC", "jobFamily": "Human Resources", "positionCode": "84767", "positionTitle": "TA Operations & Onboarding Officer O622", "departmentCode": "***************", "departmentName": "O622 TA Operations & Onboarding D4 ID", "action": "APPROVE", "counterApproval": null, "reason": "adfsf", "description": "Approved", "note": null, "isCurrent": true, "isRequestor": false, "attachments": [], "approvers": [{"createdAt": "2025-04-08T17:00:53", "updatedAt": "2025-04-08T17:00:53", "createdBy": "3021586", "updatedBy": "3021586", "id": 119688, "approverCode": "3021586", "employeeNik": "3021586", "employeeName": "ADNAN RUSDIAN", "jobCategory": "ENABLER", "jobTitle": "TA Operations & Onboarding Officer", "jobLevel": "L4 - IC", "jobFamily": "Human Resources", "positionCode": "84767", "positionTitle": "TA Operations & Onboarding Officer O622", "departmentCode": "***************", "departmentName": "O622 TA Operations & Onboarding D4 ID"}]}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T17:00:55", "createdBy": "3041347", "updatedBy": "3041347", "id": 48530, "approverCode": "3032314,3039525,5321125,3003937,ad<PERSON><PERSON><PERSON>,3021586,3038205,5378968,3035472,3016316,3035474,5346411,3020365,3009279,3003846,12790,3037007,5408010,5381470,539744,3019936,dion,3039515,5416666,3026445,3024157,3035475,3026431,3005146,3010748,3036344,5397644,5391353,qasq3,3027038,3033690,3034500,5321468", "employeeNik": "3032314,3039525,5321125,3003937,ad<PERSON><PERSON><PERSON>,3021586,3038205,5378968,3035472,3016316,3035474,5346411,3020365,3009279,3003846,12790,3037007,5408010,5381470,539744,3019936,dion,3039515,5416666,3026445,3024157,3035475,3026431,3005146,3010748,3036344,5397644,5391353,qasq3,3027038,3033690,3034500,5321468", "employeeName": "<PERSON><PERSON><PERSON><PERSON>,<PERSON> ,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,ADNA<PERSON> RUS<PERSON>,<PERSON><PERSON> ,<PERSON><PERSON>,I MADE WELI MOKSAOKA,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>RUN,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,PUJAKESUMA,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,539744,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,5416666,NAKAMI ASAKU,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,SYAQIF CONSTANTIN ARSALAN,<PERSON><PERSON>,3010748,<PERSON>,<PERSON><PERSON> ,ADENON<PERSON> AYU TAMPUBOLON,qasq3,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,YOHAN<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>", "jobCategory": "COLLECTION & RECOVERY,MONITORING & CONTROL,ENABLER,DIGITAL, DATA, & DESIGN,OPERATIONS & ADMINISTRATION,RE<PERSON>NUE GENERATOR", "jobTitle": "Loan Workout Head,Credit PreSigning Verification Specialist,Personnel Services Officer,Network Region Service & Operations Head,TA Operations & Onboarding Officer,Squad Lead,Card, Lending & Collection Incentive Management Analyst,Inspection & Valuation Officer,HR Information System Officer,Onboarding, Personnel & Benefit Services Head,Loan Disbursement & Maintenance Sr Officer,Loan Disbursement & Maintenance Officer,General Admin & GHG Management Head,Card Collection Staff,Sr Business Analyst,Card, Lending & Collection Incentive Management Head,Personnel Services & Organization Maintenance Head-<PERSON> Gunawan,HR Information System Head,Property Management Head,Inspection & Valuation Manager,Strategic Sourcing Head,Loan Disbursement & Maintenance Manager,Unsecured Collection Team Leader,Head of Loan Workout,Head of Corporate Banking", "jobLevel": "L2,L4 - IC,L5 - IC,L6 - IC,L3,L4,L5,L1", "jobFamily": "Credit Collection,Credit,Human Resources,Management,Digital Development,Data Analytics,General Administration & Secretarial,Digital Project Management,Banking Operations,Procurement,Sales", "positionCode": "88897,92162,90731,98672,84767,90741,91452,95234,99219,84769,95265,95287,ID123792,95231,103506,90941,91467,84784,84755,99029,95299,96369,95298,95230,101395,101405,88902,88900", "positionTitle": "Loan Workout Head - Corporate Banking II O822,Credit PreSigning Verification Specialist 1122,Personnel Services & Organization Maintenance Officer - PS O922,Network Region Service & Operations Head - Sumatera Region O423,TA Operations & Onboarding Officer O622,Squad Lead - Enablers Squad 1022,Card, Lending & Collection Incentive Management Analyst 1022,Inspection & Valuation Officer - Central 1 - BTO 1222,HR Information System Officer O423,Onboarding, Personnel & Benefit Services Head O622,Sr <PERSON><PERSON> Disbursement & Maintenance Officer - East - DRO 1222,<PERSON><PERSON> Disbursement & Maintenance Officer - East - DRO 1222,Area Credit Administration Head - Commercial - JKT & Lampung,Inspection & Valuation Officer - East 1 - RYD 1222,Card Collection Staff II - X-Days Jabodetabek I - Griya O823,Sr Business Analyst - RM, Compliance, Corporate Affair, Legal, Audit & HR 1022,Card, Lending & Collection Incentive Management Head 1022,Personnel Services & Organization Maintenance Head O622,HR Information System Head O622,Property Management Head O423,Inspection & Valuation Manager - Central 1 1222,Campaign Analytics Specialist O123,<PERSON>an Disbursement & Maintenance Manager - East 1222,Inspection & Valuation Manager - East 1 1222,Card Collection Staff - BE - Field - Jabodetabek - 3 O523,Unsecured Collection Team Leader - BE - Field CC - Jabodetabek - 3 O523,Head of Loan Workout - Corporate Banking O822,Loan Workout Head - Corporate Banking I O822", "departmentCode": "***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************,***************", "departmentName": "O822 Loan Workout - Corporate Banking II ID,1122 Credit PreSigning Verification D4 ID,O622 Personnel Services & Organization Maintenance D4 ID - Edited,O423 Network Region Service & Operations - Sumatera Region ID,O622 TA Operations & Onboarding D4 ID,1022 Enablers Squad ID,1022 Card, Lending & Collection Incentive Management D4 ID,1222 Inspection & Valuation - Central 1 D5 ID,O423 HR Information System ID,O622 Onboarding, Personnel & Benefit Services ID,1222 Loan Disbursement & Maintenance - East D5 ID,O424 General Admin & GHG Management ID,1222 Inspection & Valuation - East 1 D5 ID,O823 Card Collection II - X-Days Jabodetabek I D6 ID,1022 Business Analyst - RM, Compliance, Corporate Affair, Legal, Audit & HR D4 ID,1022 Card, Lending & Collection Incentive Management D3 ID,O622 Personnel Services & Organization Maintenance ID,O622 HR Information System ID,O423 Property Management D3 ID,1222 Inspection & Valuation - Central 1 D4 ID,O822 Strategic Sourcing - Non IT D3 ID,1222 Loan Disbursement & Maintenance - East D4 ID,1222 Inspection & Valuation - East 1 D4 ID,O523 Card Collection - BE - Field - Jabodetabek - 3 D6 ID,O523 Unsecured Collection - BE - Field CC - Jabodetabek - 3 D5 ID,O822 Loan Workout - Corporate Banking ID,O822 Loan Workout - Corporate Banking I ID", "action": "Pending", "counterApproval": 0, "reason": null, "description": "Pending", "note": null, "isCurrent": false, "isRequestor": false, "attachments": [], "approvers": [{"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119666, "approverCode": "3037007", "employeeNik": "3037007", "employeeName": "<PERSON><PERSON>", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Inspection & Valuation Officer", "jobLevel": "L5 - IC", "jobFamily": "Credit", "positionCode": "95231", "positionTitle": "Inspection & Valuation Officer - East 1 - RYD 1222", "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - East 1 D5 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119665, "approverCode": "12790", "employeeNik": "12790", "employeeName": "PUJAKESUMA", "jobCategory": "OPERATIONS & ADMINISTRATION", "jobTitle": "General Admin & GHG Management Head", "jobLevel": "L2", "jobFamily": "General Administration & Secretarial", "positionCode": "ID123792", "positionTitle": "Area Credit Administration Head - Commercial - JKT & Lampung", "departmentCode": "***************", "departmentName": "O424 General Admin & GHG Management ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119655, "approverCode": "3021586", "employeeNik": "3021586", "employeeName": "ADNAN RUSDIAN", "jobCategory": "ENABLER", "jobTitle": "TA Operations & Onboarding Officer", "jobLevel": "L4 - IC", "jobFamily": "Human Resources", "positionCode": "84767", "positionTitle": "TA Operations & Onboarding Officer O622", "departmentCode": "***************", "departmentName": "O622 TA Operations & Onboarding D4 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119662, "approverCode": "3020365", "employeeNik": "3020365", "employeeName": "NASRUN", "jobCategory": "ENABLER", "jobTitle": "Onboarding, Personnel & Benefit Services Head", "jobLevel": "L2", "jobFamily": "Human Resources", "positionCode": "84769", "positionTitle": "Onboarding, Personnel & Benefit Services Head O622", "departmentCode": "***************", "departmentName": "O622 Onboarding, Personnel & Benefit Services ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119673, "approverCode": "5416666", "employeeNik": "5416666", "employeeName": "5416666", "jobCategory": null, "jobTitle": null, "jobLevel": null, "jobFamily": null, "positionCode": null, "positionTitle": null, "departmentCode": null, "departmentName": null}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119677, "approverCode": "3026431", "employeeNik": "3026431", "employeeName": "SYAQIF CONSTANTIN ARSALAN", "jobCategory": "ENABLER", "jobTitle": "Strategic Sourcing Head", "jobLevel": "L3", "jobFamily": "Procurement", "positionCode": "96369", "positionTitle": "Campaign Analytics Specialist O123", "departmentCode": "***************", "departmentName": "O822 Strategic Sourcing - Non IT D3 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119652, "approverCode": "5321125", "employeeNik": "5321125", "employeeName": "<PERSON>", "jobCategory": "ENABLER", "jobTitle": "Personnel Services Officer", "jobLevel": "L4 - IC", "jobFamily": "Human Resources", "positionCode": "90731", "positionTitle": "Personnel Services & Organization Maintenance Officer - PS O922", "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance D4 ID - Edited"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119661, "approverCode": "5346411", "employeeNik": "5346411", "employeeName": "<PERSON><PERSON>", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "HR Information System Officer", "jobLevel": "L4 - IC", "jobFamily": "Digital Development", "positionCode": "99219", "positionTitle": "HR Information System Officer O423", "departmentCode": "***************", "departmentName": "O423 HR Information System ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119663, "approverCode": "3009279", "employeeNik": "3009279", "employeeName": "Subamaniam", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Loan Disbursement & Maintenance Sr Officer", "jobLevel": "L5 - IC", "jobFamily": "Credit", "positionCode": "95265", "positionTitle": "Sr Loan Disbursement & Maintenance Officer - East - DRO 1222", "departmentCode": "***************", "departmentName": "1222 Loan Disbursement & Maintenance - East D5 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119656, "approverCode": "3038205", "employeeNik": "3038205", "employeeName": "<PERSON>n ", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Squad Lead", "jobLevel": "L4 - IC", "jobFamily": "Digital Development", "positionCode": "90741", "positionTitle": "Squad Lead - Enablers Squad 1022", "departmentCode": "***************", "departmentName": "1022 Enablers Squad ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119653, "approverCode": "3003937", "employeeNik": "3003937", "employeeName": "<PERSON><PERSON><PERSON>", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Network Region Service & Operations Head", "jobLevel": "L2", "jobFamily": "Management", "positionCode": "98672", "positionTitle": "Network Region Service & Operations Head - Sumatera Region O423", "departmentCode": "***************", "departmentName": "O423 Network Region Service & Operations - Sumatera Region ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119684, "approverCode": "3027038", "employeeNik": "3027038", "employeeName": "<PERSON><PERSON><PERSON>", "jobCategory": "COLLECTION & RECOVERY", "jobTitle": "Unsecured Collection Team Leader", "jobLevel": "L5", "jobFamily": "Credit Collection", "positionCode": "101405", "positionTitle": "Unsecured Collection Team Leader - BE - Field CC - Jabodetabek - 3 O523", "departmentCode": "***************", "departmentName": "O523 Unsecured Collection - BE - Field CC - Jabodetabek - 3 D5 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119668, "approverCode": "5381470", "employeeNik": "5381470", "employeeName": "Mermaid", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Sr Business Analyst", "jobLevel": "L4 - IC", "jobFamily": "Digital Project Management", "positionCode": "90941", "positionTitle": "Sr Business Analyst - RM, Compliance, Corporate Affair, Legal, Audit & HR 1022", "departmentCode": "***************", "departmentName": "1022 Business Analyst - RM, Compliance, Corporate Affair, Legal, Audit & HR D4 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119685, "approverCode": "3033690", "employeeNik": "3033690", "employeeName": "<PERSON><PERSON>", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Head of Loan Workout", "jobLevel": "L1", "jobFamily": "Management", "positionCode": "88902", "positionTitle": "Head of Loan Workout - Corporate Banking O822", "departmentCode": "***************", "departmentName": "O822 Loan Workout - Corporate Banking ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119657, "approverCode": "5378968", "employeeNik": "5378968", "employeeName": "<PERSON><PERSON>", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Card, Lending & Collection Incentive Management Analyst", "jobLevel": "L4 - IC", "jobFamily": "Data Analytics", "positionCode": "91452", "positionTitle": "Card, Lending & Collection Incentive Management Analyst 1022", "departmentCode": "***************", "departmentName": "1022 Card, Lending & Collection Incentive Management D4 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119681, "approverCode": "5397644", "employeeNik": "5397644", "employeeName": "Hoo ", "jobCategory": "ENABLER", "jobTitle": "Personnel Services Officer", "jobLevel": "L4 - IC", "jobFamily": "Human Resources", "positionCode": "90731", "positionTitle": "Personnel Services & Organization Maintenance Officer - PS O922", "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance D4 ID - Edited"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119650, "approverCode": "3032314", "employeeNik": "3032314", "employeeName": "DIETER", "jobCategory": "COLLECTION & RECOVERY", "jobTitle": "<PERSON>an Workout Head", "jobLevel": "L2", "jobFamily": "Credit Collection", "positionCode": "88897", "positionTitle": "Loan Workout Head - Corporate Banking II O822", "departmentCode": "***************", "departmentName": "O822 Loan Workout - Corporate Banking II ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119669, "approverCode": "539744", "employeeNik": "539744", "employeeName": "539744", "jobCategory": null, "jobTitle": null, "jobLevel": null, "jobFamily": null, "positionCode": null, "positionTitle": null, "departmentCode": null, "departmentName": null}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119679, "approverCode": "3010748", "employeeNik": "3010748", "employeeName": "3010748", "jobCategory": null, "jobTitle": null, "jobLevel": null, "jobFamily": null, "positionCode": null, "positionTitle": null, "departmentCode": null, "departmentName": null}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119664, "approverCode": "3003846", "employeeNik": "3003846", "employeeName": "<PERSON><PERSON><PERSON>", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Loan Disbursement & Maintenance Officer", "jobLevel": "L5 - IC", "jobFamily": "Credit", "positionCode": "95287", "positionTitle": "Loan Disbursement & Maintenance Officer - East - DRO 1222", "departmentCode": "***************", "departmentName": "1222 Loan Disbursement & Maintenance - East D5 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119674, "approverCode": "3026445", "employeeNik": "3026445", "employeeName": "NAKAMI ASAKU", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "HR Information System Head", "jobLevel": "L3", "jobFamily": "Banking Operations", "positionCode": "84755", "positionTitle": "HR Information System Head O622", "departmentCode": "***************", "departmentName": "O622 HR Information System ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119683, "approverCode": "qasq3", "employeeNik": "qasq3", "employeeName": "qasq3", "jobCategory": null, "jobTitle": null, "jobLevel": null, "jobFamily": null, "positionCode": null, "positionTitle": null, "departmentCode": null, "departmentName": null}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119676, "approverCode": "3035475", "employeeNik": "3035475", "employeeName": "<PERSON><PERSON><PERSON>", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Inspection & Valuation Manager", "jobLevel": "L4", "jobFamily": "Credit", "positionCode": "95299", "positionTitle": "Inspection & Valuation Manager - Central 1 1222", "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - Central 1 D4 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119686, "approverCode": "3034500", "employeeNik": "3034500", "employeeName": "YOHANA", "jobCategory": "REVENUE GENERATOR", "jobTitle": "Head of Corporate Banking", "jobLevel": "L1", "jobFamily": "Sales", "positionCode": "88900", "positionTitle": "Loan Workout Head - Corporate Banking I O822", "departmentCode": "***************", "departmentName": "O822 Loan Workout - Corporate Banking I ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119654, "approverCode": "admin<PERSON><PERSON>", "employeeNik": "admin<PERSON><PERSON>", "employeeName": "admin<PERSON><PERSON>", "jobCategory": null, "jobTitle": null, "jobLevel": null, "jobFamily": null, "positionCode": null, "positionTitle": null, "departmentCode": null, "departmentName": null}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119670, "approverCode": "3019936", "employeeNik": "3019936", "employeeName": "<PERSON><PERSON>", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Card, Lending & Collection Incentive Management Head", "jobLevel": "L3", "jobFamily": "Data Analytics", "positionCode": "91467", "positionTitle": "Card, Lending & Collection Incentive Management Head 1022", "departmentCode": "***************", "departmentName": "1022 Card, Lending & Collection Incentive Management D3 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119687, "approverCode": "5321468", "employeeNik": "5321468", "employeeName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jobCategory": "ENABLER", "jobTitle": "Personnel Services Officer", "jobLevel": "L4 - IC", "jobFamily": "Human Resources", "positionCode": "90731", "positionTitle": "Personnel Services & Organization Maintenance Officer - PS O922", "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance D4 ID - Edited"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119659, "approverCode": "3016316", "employeeNik": "3016316", "employeeName": "<PERSON><PERSON><PERSON>", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "HR Information System Officer", "jobLevel": "L4 - IC", "jobFamily": "Digital Development", "positionCode": "99219", "positionTitle": "HR Information System Officer O423", "departmentCode": "***************", "departmentName": "O423 HR Information System ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119675, "approverCode": "3024157", "employeeNik": "3024157", "employeeName": "<PERSON><PERSON>", "jobCategory": "OPERATIONS & ADMINISTRATION", "jobTitle": "Property Management Head", "jobLevel": "L3", "jobFamily": "General Administration & Secretarial", "positionCode": "99029", "positionTitle": "Property Management Head O423", "departmentCode": "***************", "departmentName": "O423 Property Management D3 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119680, "approverCode": "3036344", "employeeNik": "3036344", "employeeName": "<PERSON>", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Inspection & Valuation Manager", "jobLevel": "L4", "jobFamily": "Credit", "positionCode": "95230", "positionTitle": "Inspection & Valuation Manager - East 1 1222", "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - East 1 D4 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119660, "approverCode": "3035474", "employeeNik": "3035474", "employeeName": "<PERSON>", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Inspection & Valuation Officer", "jobLevel": "L5 - IC", "jobFamily": "Credit", "positionCode": "95234", "positionTitle": "Inspection & Valuation Officer - Central 1 - BTO 1222", "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - Central 1 D5 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119658, "approverCode": "3035472", "employeeNik": "3035472", "employeeName": "I MADE WELI MOKSAOKA", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Inspection & Valuation Officer", "jobLevel": "L5 - IC", "jobFamily": "Credit", "positionCode": "95234", "positionTitle": "Inspection & Valuation Officer - Central 1 - BTO 1222", "departmentCode": "***************", "departmentName": "1222 Inspection & Valuation - Central 1 D5 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119678, "approverCode": "3005146", "employeeNik": "3005146", "employeeName": "<PERSON><PERSON>", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Loan Disbursement & Maintenance Manager", "jobLevel": "L4", "jobFamily": "Credit", "positionCode": "95298", "positionTitle": "Loan Disbursement & Maintenance Manager - East 1222", "departmentCode": "***************", "departmentName": "1222 Loan Disbursement & Maintenance - East D4 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119667, "approverCode": "5408010", "employeeNik": "5408010", "employeeName": "<PERSON><PERSON><PERSON>", "jobCategory": "COLLECTION & RECOVERY", "jobTitle": "Card Collection Staff", "jobLevel": "L6 - IC", "jobFamily": "Credit Collection", "positionCode": "103506", "positionTitle": "Card Collection Staff II - X-Days Jabodetabek I - Griya O823", "departmentCode": "***************", "departmentName": "O823 Card Collection II - X-Days Jabodetabek I D6 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119671, "approverCode": "dion", "employeeNik": "dion", "employeeName": "dion", "jobCategory": null, "jobTitle": null, "jobLevel": null, "jobFamily": null, "positionCode": null, "positionTitle": null, "departmentCode": null, "departmentName": null}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119672, "approverCode": "3039515", "employeeNik": "3039515", "employeeName": "<PERSON>", "jobCategory": "ENABLER", "jobTitle": "Personnel Services & Organization Maintenance Head-<PERSON> <PERSON><PERSON><PERSON>", "jobLevel": "L3", "jobFamily": "Human Resources", "positionCode": "84784", "positionTitle": "Personnel Services & Organization Maintenance Head O622", "departmentCode": "***************", "departmentName": "O622 Personnel Services & Organization Maintenance ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119682, "approverCode": "5391353", "employeeNik": "5391353", "employeeName": "ADENONI AYU TAMPUBOLON", "jobCategory": "COLLECTION & RECOVERY", "jobTitle": "Card Collection Staff", "jobLevel": "L6 - IC", "jobFamily": "Credit Collection", "positionCode": "101395", "positionTitle": "Card Collection Staff - BE - Field - Jabodetabek - 3 O523", "departmentCode": "***************", "departmentName": "O523 Card Collection - BE - Field - Jabodetabek - 3 D6 ID"}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119651, "approverCode": "3039525", "employeeNik": "3039525", "employeeName": "En ", "jobCategory": "MONITORING & CONTROL", "jobTitle": "Credit PreSigning Verification Specialist", "jobLevel": "L4 - IC", "jobFamily": "Credit", "positionCode": "92162", "positionTitle": "Credit PreSigning Verification Specialist 1122", "departmentCode": "***************", "departmentName": "1122 Credit PreSigning Verification D4 ID"}]}, {"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T17:00:55", "createdBy": "3041347", "updatedBy": "3041347", "id": 48529, "approverCode": "3041347", "employeeNik": "3041347", "employeeName": "Nor'azam", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Cost Management Head", "jobLevel": "L3", "jobFamily": "Finance", "positionCode": "IDP20241016153258381", "positionTitle": "Test Under Y", "departmentCode": "300003367915888", "departmentName": "1122 Cost Management ID", "action": "NEW_TRANSACTION", "counterApproval": null, "reason": "", "description": "Submitted", "note": null, "isCurrent": false, "isRequestor": true, "attachments": [], "approvers": [{"createdAt": "2025-04-08T16:59:33", "updatedAt": "2025-04-08T16:59:33", "createdBy": "3041347", "updatedBy": "3041347", "id": 119649, "approverCode": "3041347", "employeeNik": "3041347", "employeeName": "Nor'azam", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobTitle": "Cost Management Head", "jobLevel": "L3", "jobFamily": "Finance", "positionCode": "IDP20241016153258381", "positionTitle": "Test Under Y", "departmentCode": "300003367915888", "departmentName": "1122 Cost Management ID"}]}], "createdByName": "Nor'azam", "processName": "Manage Person", "submitter": {"employeeNumber": "3041347", "name": "NOR'AZAM AZAM", "job": {"id": 12022, "fsJobId": ***************, "jobCode": "ID256542", "jobTitle": "Developer", "jobFamily": "Digital Development", "dateFrom": "1951-01-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "Application Development", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "position": {"id": 49618, "fsPositionId": 300003207703296, "positionCode": "90754", "positionTitle": "Developer - EBB Squad 1022", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "3", "headCount": "3", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "location": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "department": {"id": 98294, "fsDepartmentId": 300003207320718, "departmentCode": "300003207320718", "departmentName": "1022 EBB Squad ID", "type": "L4", "dateFrom": "2022-10-01", "romDivision": "Group Technology"}}, "processOwner": {"employeeNumber": "3041347", "name": "NOR'AZAM AZAM", "job": {"id": 12022, "fsJobId": ***************, "jobCode": "ID256542", "jobTitle": "Developer", "jobFamily": "Digital Development", "dateFrom": "1951-01-01", "jobCategory": "DIGITAL, DATA, & DESIGN", "jobLevel": "L4 - IC", "functionCode": "Application Development", "jobClassification": "Bonus Based", "jobField": null, "jobDescription1": null, "jobDescription2": null, "jobCertification": null, "uniqueJob": null}, "position": {"id": 49618, "fsPositionId": 300003207703296, "positionCode": "90754", "positionTitle": "Developer - EBB Squad 1022", "type": "POOLED", "hiringStatus": "APPROVED", "fte": "3", "headCount": "3", "reportingLine": "D4", "parentCode": null, "permanentFlag": "R", "positionStatus": null, "keyPosition": "N", "workplaceCategory": "Flex", "highriskPosition": "N", "executiveOfficer": null, "criticalPosition": null}, "location": {"id": 4909, "fsLocationId": 300002788524028, "locationCode": "IDO522433", "locationName": "O522 Kantor P<PERSON>t - Griya Niaga 2", "description": "O522 Kantor P<PERSON>t - Griya Niaga 2", "city": "NGAWI", "province": "JAWA TIMUR", "country": "Indonesia", "initialBranch": "KP", "branchCode": null, "branchSize": null, "branchType": "KP"}, "department": {"id": 98294, "fsDepartmentId": 300003207320718, "departmentCode": "300003207320718", "departmentName": "1022 EBB Squad ID", "type": "L4", "dateFrom": "2022-10-01", "romDivision": "Group Technology"}}, "dataDetail": "[{\"header\":\"Manage Person\",\"card\":[{\"title\":\"Address 1\",\"data\":[{\"label\":\"action\",\"type\":\"text\",\"value\":null,\"description\":null},{\"label\":\"country\",\"type\":\"text\",\"value\":\"Armenia\",\"description\":null},{\"label\":\"rt\",\"type\":\"text\",\"value\":\"008\",\"description\":null},{\"label\":\"address\",\"type\":\"text\",\"value\":\"GOLD LINE  , RT 008, RW 010, <PERSON><PERSON><PERSON>an SFSFSF, Kecamatan TESSSSS , Buleleng , Bali , Kode Pos :  , Armenia\",\"description\":null},{\"label\":\"city\",\"type\":\"text\",\"value\":\"Buleleng\",\"description\":null},{\"label\":\"rw\",\"type\":\"text\",\"value\":\"010\",\"description\":null},{\"label\":\"addressType\",\"type\":\"text\",\"value\":\"Mailing Address \",\"description\":null},{\"label\":\"postalCode\",\"type\":\"text\",\"value\":\"\",\"description\":null},{\"label\":\"fsAddressId\",\"type\":\"text\",\"value\":****************,\"description\":null},{\"label\":\"kelurahan\",\"type\":\"text\",\"value\":\"SFSFSF\",\"description\":null},{\"label\":\"province\",\"type\":\"text\",\"value\":\"Bali\",\"description\":null},{\"label\":\"kecamatan\",\"type\":\"text\",\"value\":\"TESSSSS\",\"description\":null},{\"label\":\"addressLine1\",\"type\":\"text\",\"value\":\"GOLD LINE\",\"description\":null},{\"label\":\"addressLine2\",\"type\":\"text\",\"value\":\"\",\"description\":null},{\"label\":\"id\",\"type\":\"text\",\"value\":46343,\"description\":null},{\"label\":\"startDate\",\"type\":\"text\",\"value\":\"2025-02-05\",\"description\":null}]},{\"title\":\"Address 2\",\"data\":[{\"label\":\"action\",\"type\":\"text\",\"value\":null,\"description\":null},{\"label\":\"country\",\"type\":\"text\",\"value\":\"Indonesia\",\"description\":null},{\"label\":\"rt\",\"type\":\"text\",\"value\":\"12\",\"description\":null},{\"label\":\"address\",\"type\":\"text\",\"value\":\"STATION ROAD , RT 12, RW 9, Kelurahan TEBET TIMUR, Kecamatan TEBET , INI CITY , INI PROVINCE , Kode Pos :  , Indonesia\",\"description\":null},{\"label\":\"city\",\"type\":\"text\",\"value\":\"INI CITY\",\"description\":null},{\"label\":\"rw\",\"type\":\"text\",\"value\":\"9\",\"description\":null},{\"label\":\"addressType\",\"type\":\"text\",\"value\":\"Domicile\",\"description\":null},{\"label\":\"postalCode\",\"type\":\"text\",\"value\":\"\",\"description\":null},{\"label\":\"fsAddressId\",\"type\":\"text\",\"value\":300001003701926,\"description\":null},{\"label\":\"kelurahan\",\"type\":\"text\",\"value\":\"TEBET TIMUR\",\"description\":null},{\"label\":\"province\",\"type\":\"text\",\"value\":\"INI PROVINCE\",\"description\":null},{\"label\":\"kecamatan\",\"type\":\"text\",\"value\":\"TEBET\",\"description\":null},{\"label\":\"addressLine1\",\"type\":\"text\",\"value\":\"STATION\",\"description\":null},{\"label\":\"addressLine2\",\"type\":\"text\",\"value\":\"ROAD\",\"description\":null},{\"label\":\"id\",\"type\":\"text\",\"value\":46556,\"description\":null},{\"label\":\"startDate\",\"type\":\"text\",\"value\":\"2021-04-10\",\"description\":null}]},{\"title\":\"Address 3\",\"data\":[{\"label\":\"action\",\"type\":\"text\",\"value\":\"edit\",\"description\":null},{\"label\":\"country\",\"type\":\"text\",\"value\":\"Indonesia\",\"description\":null},{\"label\":\"rt\",\"type\":\"text\",\"value\":\"001\",\"description\":null},{\"label\":\"address\",\"type\":\"text\",\"value\":\"ROAD 1 ROAD 2 , RT 001, RW 002, Kelurahan SERUA, Kecamatan BOJONGSARI , Badung , Bali , Kode Pos : 16517 , Indonesia\",\"description\":null},{\"label\":\"city\",\"type\":\"text\",\"value\":\"Badung\",\"description\":null},{\"label\":\"rw\",\"type\":\"text\",\"value\":\"002\",\"description\":null},{\"label\":\"addressType\",\"type\":\"text\",\"value\":\"KTP\",\"description\":null},{\"label\":\"postalCode\",\"type\":\"text\",\"value\":\"16517\",\"description\":null},{\"label\":\"fsAddressId\",\"type\":\"text\",\"value\":300001003701916,\"description\":null},{\"label\":\"kelurahan\",\"type\":\"text\",\"value\":\"SERUA\",\"description\":null},{\"label\":\"province\",\"type\":\"text\",\"value\":\"Bali\",\"description\":null},{\"label\":\"kecamatan\",\"type\":\"text\",\"value\":\"BOJONGSARI\",\"description\":null},{\"label\":\"addressLine1\",\"type\":\"text\",\"value\":\"ROAD 1\",\"description\":null},{\"label\":\"addressLine2\",\"type\":\"text\",\"value\":\"ROAD 2\",\"description\":null},{\"label\":\"id\",\"type\":\"text\",\"value\":46735,\"description\":null},{\"label\":\"startDate\",\"type\":\"text\",\"value\":null,\"description\":null}]}]}]", "actions": null, "isUsingAnchor": false, "anchorWeb": null, "anchorMobile": null}}