# Staggered Payment API Specification

This document provides a comprehensive specification for all endpoints in the Staggered Payment API.

## Base URL

`/api/staggered`

## Common Response Format

All API responses follow a standard format:

### Success Response

```json
{
  "code": 200,
  "status": "OK",
  "message": "Operation completed successfully",
  "data": {}
}
```

### Error Response

```json
{
  "code": 400,
  "status": "ERROR",
  "message": "Error description",
  "data": null
}
```

## 1. Bulk Validation

Validate an Excel file containing multiple payment records.

**Endpoint:** `POST /api/staggered/bulk-validate`

**Content-Type:** `multipart/form-data`

**Request Parameters:**

| Parameter | Type | Required | Description                        |
| --------- | ---- | -------- | ---------------------------------- |
| `file`    | File | Yes      | Excel file containing payment data |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "File validated successfully",
  "data": [
    {
      "nip": "123456789",
      "name": "John Doe",
      "grade": "U5",
      "paymentType": "Bonus Staggered",
      "amount": 5000000.0,
      "description": "Performance bonus",
      "monthOfProcess": "January",
      "yearOfProcess": "2024",
      "directorate": "IT",
      "slik": "-",
      "sanction": "-",
      "terminationDate": "-",
      "eligible": true,
      "nipValid": "NIP is valid"
    }
  ]
}
```

**Error Responses:**

| Status Code | Description                                       | Response Body                                                                        |
| ----------- | ------------------------------------------------- | ------------------------------------------------------------------------------------ |
| 400         | Bad Request - Invalid file format or missing file | `{"code": 400, "status": "ERROR", "message": "Invalid file format", "data": null}`   |
| 500         | Internal Server Error                             | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}` |

## 2. Single Validation

Validate a single payment record.

**Endpoint:** `POST /api/staggered/single-validate`

**Content-Type:** `application/json`

**Request Body:**

```json
{
  "nip": "123456789",
  "name": "John Doe",
  "grade": "U5",
  "paymentType": "Bonus Staggered",
  "amount": 5000000.0,
  "description": "Performance bonus",
  "monthOfProcess": "January",
  "yearOfProcess": "2024"
}
```

**Request Body Validation:**

| Field            | Type       | Required | Validation | Description             |
| ---------------- | ---------- | -------- | ---------- | ----------------------- |
| `nip`            | String     | Yes      | @NotBlank  | Employee ID             |
| `name`           | String     | Yes      | @NotBlank  | Employee name           |
| `grade`          | String     | Yes      | @NotBlank  | Employee grade (U1-U11) |
| `paymentType`    | String     | Yes      | @NotBlank  | Type of payment         |
| `amount`         | BigDecimal | Yes      | @NotNull   | Payment amount          |
| `description`    | String     | Yes      | @NotBlank  | Payment description     |
| `monthOfProcess` | String     | Yes      | @NotBlank  | Month of processing     |
| `yearOfProcess`  | String     | Yes      | @NotBlank  | Year of processing      |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Payment data validated successfully",
  "data": {
    "nip": "123456789",
    "name": "John Doe",
    "grade": "U5",
    "paymentType": "Bonus Staggered",
    "amount": 5000000.0,
    "description": "Performance bonus",
    "monthOfProcess": "January",
    "yearOfProcess": "2024",
    "directorate": "IT",
    "slik": "-",
    "sanction": "-",
    "terminationDate": "-",
    "eligible": true,
    "nipValid": "NIP is valid"
  }
}
```

**Error Responses:**

| Status Code | Description                     | Response Body                                                                        |
| ----------- | ------------------------------- | ------------------------------------------------------------------------------------ |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "NIP is required", "data": null}`       |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}` |

## 3. Get Submission Template

Download the Excel template file for staggered payment submissions.

**Endpoint:** `GET /api/staggered/template-submission`

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Headers:** `Content-Disposition: attachment; filename="submission_template.xlsx"`
- **Body:** Binary Excel file content

**Error Responses:**

| Status Code | Description                                         |
| ----------- | --------------------------------------------------- |
| 500         | Internal Server Error - Failed to generate template |

## 4. Get Payment Template

Download the Excel template file for payment updates.

**Endpoint:** `GET /api/staggered/template-payment`

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Headers:** `Content-Disposition: attachment; filename="payment_template.xlsx"`
- **Body:** Binary Excel file content

**Error Responses:**

| Status Code | Description                                         |
| ----------- | --------------------------------------------------- |
| 500         | Internal Server Error - Failed to generate template |

## 5. Bulk Submission

Submit multiple payment requests from an Excel file with optional supporting documents.

**Endpoint:** `POST /api/staggered/bulk-submission`

**Content-Type:** `multipart/form-data`

**Request Parameters:**

| Parameter         | Type   | Required | Description                        |
| ----------------- | ------ | -------- | ---------------------------------- |
| `mainFile`        | File   | Yes      | Excel file containing payment data |
| `supportingFiles` | File[] | No       | Array of supporting documents      |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
[
  {
    "code": 200,
    "status": "OK",
    "message": "Submission received successfully for NIP: 123456789",
    "data": null
  },
  {
    "code": 200,
    "status": "OK",
    "message": "Submission received successfully for NIP: 987654321",
    "data": null
  }
]
```

**Error Responses:**

| Status Code | Description                        | Response Body                                                                                    |
| ----------- | ---------------------------------- | ------------------------------------------------------------------------------------------------ |
| 400         | Bad Request - Invalid file or data | `[{"code": 400, "status": "ERROR", "message": "Invalid data for NIP: 123456789", "data": null}]` |
| 500         | Internal Server Error              | `[{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}]`           |

## 6. Single Payment Submission

Submit a single payment request with optional supporting documents.

**Endpoint:** `POST /api/staggered/single-submission`

**Content-Type:** `multipart/form-data`

**Request Parameters:**

| Parameter         | Type   | Required | Description                                                       |
| ----------------- | ------ | -------- | ----------------------------------------------------------------- |
| `data`            | JSON   | Yes      | Single payment request data (same structure as single validation) |
| `supportingFiles` | File[] | No       | Array of supporting documents                                     |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Submission received successfully for NIP: 123456789",
  "data": null
}
```

**Error Responses:**

| Status Code | Description                     | Response Body                                                                        |
| ----------- | ------------------------------- | ------------------------------------------------------------------------------------ |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "NIP is required", "data": null}`       |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}` |

## 7. Submission History

Retrieve submission history with pagination and filtering options.

**Endpoint:** `GET /api/staggered/submission/history`

**Request Parameters:**

| Parameter         | Type    | Required | Default   | Description                       |
| ----------------- | ------- | -------- | --------- | --------------------------------- |
| `page`            | Integer | No       | 0         | Page number (zero-based)          |
| `size`            | Integer | No       | 10        | Number of items per page          |
| `sort`            | String  | No       | "id,desc" | Sorting parameters                |
| `status`          | String  | No       | null      | Filter by submission status       |
| `startDate`       | String  | No       | null      | Filter by start date (yyyy-MM-dd) |
| `endDate`         | String  | No       | null      | Filter by end date (yyyy-MM-dd)   |
| `currentUserOnly` | Boolean | No       | false     | Filter by current user only       |
| `nip`             | String  | No       | null      | Filter by employee ID             |
| `name`            | String  | No       | null      | Filter by employee name           |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Submissions retrieved successfully",
  "data": {
    "content": [
      {
        "id": 123,
        "taskName": "TASK-001",
        "referenceNumber": "SP-1234ABCD",
        "createdBy": "123456789",
        "submitterName": "John Doe",
        "submitterJob": "Developer",
        "status": "Waiting for Approval",
        "nip": "123456789",
        "name": "John Doe",
        "grade": "U5",
        "paymentType": "Bonus Staggered",
        "amount": 5000000.0,
        "description": "Performance bonus",
        "monthOfProcess": "January",
        "yearOfProcess": "2024",
        "directorate": "IT",
        "slik": "-",
        "sanction": "-",
        "terminationDate": "-",
        "eligible": true,
        "currentReviewer": "<EMAIL>",
        "paymentDate": "-",
        "remarks": "-"
      }
    ],
    "pageable": {
      "sort": {
        "empty": false,
        "sorted": true,
        "unsorted": false
      },
      "offset": 0,
      "pageNumber": 0,
      "pageSize": 10,
      "paged": true,
      "unpaged": false
    },
    "totalElements": 100,
    "totalPages": 10,
    "last": false,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": false,
      "sorted": true,
      "unsorted": false
    },
    "numberOfElements": 10,
    "first": true,
    "empty": false
  }
}
```

**Error Responses:**

| Status Code | Description                          | Response Body                                                                        |
| ----------- | ------------------------------------ | ------------------------------------------------------------------------------------ |
| 400         | Bad Request - Invalid parameters     | `{"code": 400, "status": "ERROR", "message": "Invalid date format", "data": null}`   |
| 403         | Forbidden - Insufficient permissions | `{"code": 403, "status": "ERROR", "message": "Access denied", "data": null}`         |
| 500         | Internal Server Error                | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}` |

## 8. Submission Detail

Retrieve detailed information for a specific submission.

**Endpoint:** `GET /api/staggered/submission/history/{id}`

**Path Parameters:**

| Parameter | Type | Required | Description          |
| --------- | ---- | -------- | -------------------- |
| `id`      | Long | Yes      | ID of the submission |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Submission detail retrieved successfully",
  "data": {
    "id": 123,
    "taskName": "TASK-001",
    "referenceNumber": "SP-1234ABCD",
    "createdBy": "123456789",
    "submitterName": "John Doe",
    "submitterJob": "Developer",
    "status": "Waiting for Approval",
    "nip": "123456789",
    "name": "John Doe",
    "grade": "U5",
    "paymentType": "Bonus Staggered",
    "amount": 5000000.0,
    "description": "Performance bonus",
    "monthOfProcess": "January",
    "yearOfProcess": "2024",
    "directorate": "IT",
    "slik": "-",
    "sanction": "-",
    "terminationDate": "-",
    "eligible": true,
    "currentReviewer": "<EMAIL>",
    "paymentDate": "-",
    "remarks": "-",
    "supportingFiles": [
      {
        "id": 456,
        "fileName": "document.pdf"
      }
    ]
  }
}
```

**Error Responses:**

| Status Code | Description                      | Response Body                                                                                   |
| ----------- | -------------------------------- | ----------------------------------------------------------------------------------------------- |
| 400         | Bad Request - Invalid ID format  | `{"code": 400, "status": "ERROR", "message": "Invalid ID format", "data": null}`                |
| 403         | Forbidden - Access denied        | `{"code": 403, "status": "ERROR", "message": "Access denied to this submission", "data": null}` |
| 404         | Not Found - Submission not found | `{"code": 404, "status": "ERROR", "message": "Submission not found", "data": null}`             |
| 500         | Internal Server Error            | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}`            |

## 9. Transaction Console Log

Retrieve transaction console log for a specific task.

**Endpoint:** `GET /api/staggered/log-{taskname}`

**Path Parameters:**

| Parameter  | Type   | Required | Description      |
| ---------- | ------ | -------- | ---------------- |
| `taskname` | String | Yes      | Name of the task |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Transaction console log retrieved successfully",
  "data": {
    "logs": "Transaction log details..."
  }
}
```

**Error Responses:**

| Status Code | Description                | Response Body                                                                        |
| ----------- | -------------------------- | ------------------------------------------------------------------------------------ |
| 404         | Not Found - Task not found | `{"code": 404, "status": "ERROR", "message": "Task not found", "data": null}`        |
| 500         | Internal Server Error      | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}` |

## 10. Approval Task List

Retrieve approval task list with pagination and filtering options.

**Endpoint:** `GET /api/staggered/list-approval`

**Request Parameters:**

| Parameter   | Type    | Required | Default   | Description                       |
| ----------- | ------- | -------- | --------- | --------------------------------- |
| `page`      | Integer | No       | 0         | Page number (zero-based)          |
| `size`      | Integer | No       | 10        | Number of items per page          |
| `sort`      | String  | No       | "id,desc" | Sorting parameters                |
| `status`    | String  | No       | null      | Filter by submission status       |
| `startDate` | String  | No       | null      | Filter by start date (yyyy-MM-dd) |
| `endDate`   | String  | No       | null      | Filter by end date (yyyy-MM-dd)   |
| `nip`       | String  | No       | null      | Filter by employee ID             |
| `name`      | String  | No       | null      | Filter by employee name           |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Approval tasks retrieved successfully",
  "data": {
    "content": [
      {
        "id": 123,
        "taskName": "TASK-001",
        "referenceNumber": "SP-1234ABCD",
        "submitterName": "John Doe",
        "submitterJob": "Developer",
        "status": "Waiting for Approval",
        "nip": "123456789",
        "name": "John Doe",
        "grade": "U5",
        "paymentType": "Bonus Staggered",
        "amount": 5000000.0,
        "description": "Performance bonus",
        "monthOfProcess": "January",
        "yearOfProcess": "2024",
        "directorate": "IT",
        "slik": "-",
        "sanction": "-",
        "terminationDate": "-",
        "eligible": true,
        "paymentDate": "-",
        "remarks": "-"
      }
    ],
    "pageable": {
      "sort": {
        "empty": false,
        "sorted": true,
        "unsorted": false
      },
      "offset": 0,
      "pageNumber": 0,
      "pageSize": 10,
      "paged": true,
      "unpaged": false
    },
    "totalElements": 50,
    "totalPages": 5,
    "last": false,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": false,
      "sorted": true,
      "unsorted": false
    },
    "numberOfElements": 10,
    "first": true,
    "empty": false
  }
}
```

**Error Responses:**

| Status Code | Description                          | Response Body                                                                        |
| ----------- | ------------------------------------ | ------------------------------------------------------------------------------------ |
| 400         | Bad Request - Invalid parameters     | `{"code": 400, "status": "ERROR", "message": "Invalid date format", "data": null}`   |
| 403         | Forbidden - Insufficient permissions | `{"code": 403, "status": "ERROR", "message": "Access denied", "data": null}`         |
| 500         | Internal Server Error                | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}` |

## 11. Download Supporting File

Download a supporting file for a specific submission.

**Endpoint:** `GET /api/staggered/download/{submissionId}/supporting/{fileId}`

**Path Parameters:**

| Parameter      | Type | Required | Description               |
| -------------- | ---- | -------- | ------------------------- |
| `submissionId` | Long | Yes      | ID of the submission      |
| `fileId`       | Long | Yes      | ID of the supporting file |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/octet-stream`
- **Headers:** `Content-Disposition: attachment; filename="filename.extension"`
- **Body:** Binary file content

**Error Responses:**

| Status Code | Description                              | Response Body                      |
| ----------- | ---------------------------------------- | ---------------------------------- |
| 404         | Not Found - Submission or file not found | No response body (binary endpoint) |
| 500         | Internal Server Error                    | No response body (binary endpoint) |

## 12. Bulk Payment Update

Update payment information from an Excel file.

**Endpoint:** `POST /api/staggered/bulk-payment`

**Content-Type:** `multipart/form-data`

**Request Parameters:**

| Parameter | Type | Required | Description                               |
| --------- | ---- | -------- | ----------------------------------------- |
| `file`    | File | Yes      | Excel file containing payment update data |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Successfully updated 5 payment(s). Failed to update 0 payment(s).",
  "data": {
    "SP-1234ABCD": "Success",
    "SP-5678EFGH": "Success",
    "SP-9012IJKL": "Success",
    "SP-3456MNOP": "Success",
    "SP-7890QRST": "Success"
  }
}
```

**Error Responses:**

| Status Code | Description                       | Response Body                                                                        |
| ----------- | --------------------------------- | ------------------------------------------------------------------------------------ |
| 400         | Bad Request - Invalid file format | `{"code": 400, "status": "ERROR", "message": "Invalid file format", "data": null}`   |
| 500         | Internal Server Error             | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}` |

## 13. Single Payment Update

Update payment information from JSON data.

**Endpoint:** `POST /api/staggered/single-payment`

**Content-Type:** `application/json`

**Request Body:**

```json
[
  {
    "referenceNumber": "SP-1234ABCD",
    "paymentDate": "2024-01-15"
  },
  {
    "referenceNumber": "SP-5678EFGH",
    "paymentDate": "2024-01-16"
  }
]
```

**Request Body Validation:**

| Field             | Type      | Required | Description                        |
| ----------------- | --------- | -------- | ---------------------------------- |
| `referenceNumber` | String    | Yes      | Reference number of the submission |
| `paymentDate`     | LocalDate | Yes      | Payment date (yyyy-MM-dd format)   |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Successfully updated 2 payment(s). Failed to update 0 payment(s).",
  "data": {
    "SP-1234ABCD": "Success",
    "SP-5678EFGH": "Success"
  }
}
```

**Error Responses:**

| Status Code | Description                     | Response Body                                                                         |
| ----------- | ------------------------------- | ------------------------------------------------------------------------------------- |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "Invalid request format", "data": null}` |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}`  |

## 14. Generate Payment Excel

Generate and download payment data in Excel format.

**Endpoint:** `POST /api/staggered/generate-payment`

**Content-Type:** `application/json`

**Request Body:**

```json
{
  "yearOfProcess": "2024",
  "monthOfProcess": "January"
}
```

**Request Body Validation:**

| Field            | Type   | Required | Description                        |
| ---------------- | ------ | -------- | ---------------------------------- |
| `yearOfProcess`  | String | Yes      | Year of process (e.g., "2024")     |
| `monthOfProcess` | String | Yes      | Month of process (e.g., "January") |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Headers:** `Content-Disposition: attachment; filename=payment_export_YYYYMMDD_HHMMSS.xlsx`
- **Body:** Binary Excel file content

**Error Responses:**

| Status Code | Description                     | Response Body                                                                                   |
| ----------- | ------------------------------- | ----------------------------------------------------------------------------------------------- |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "Year of process is required", "data": null}`      |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Failed to generate payment Excel", "data": null}` |

## 15. Process Action

Process approval actions on submissions (asynchronous).

**Endpoint:** `POST /api/staggered/action`

**Content-Type:** `application/json`

**Request Body:**

```json
{
  "taskNames": ["TASK-001", "TASK-002", "TASK-003"],
  "action": "APPROVE",
  "reason": "Payment approved after verification"
}
```

**Request Body Validation:**

| Field       | Type         | Required | Description                                      |
| ----------- | ------------ | -------- | ------------------------------------------------ |
| `taskNames` | List<String> | Yes      | List of task names to process                    |
| `action`    | String       | Yes      | Action to perform: "APPROVE", "REJECT", "REVISE" |
| `reason`    | String       | No       | Reason for the action                            |

**Success Response:**

- **Status Code:** 202 Accepted
- **Content-Type:** `application/json`

```json
{
  "code": 202,
  "status": "ACCEPTED",
  "message": "Action request accepted. Processing 3 task(s) with action 'APPROVE' in the background."
}
```

**Error Responses:**

| Status Code | Description                     | Response Body                                                                             |
| ----------- | ------------------------------- | ----------------------------------------------------------------------------------------- |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "Task names are required", "data": null}`    |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Failed to start processing", "data": null}` |

## 16. Generate Report

Generate and download report data in Excel format.

**Endpoint:** `POST /api/staggered/report`

**Content-Type:** `application/json`

**Request Body:**

```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "status": "Approved"
}
```

**Request Body Validation:**

| Field       | Type   | Required | Description                    |
| ----------- | ------ | -------- | ------------------------------ |
| `startDate` | String | No       | Start date filter (yyyy-MM-dd) |
| `endDate`   | String | No       | End date filter (yyyy-MM-dd)   |
| `status`    | String | No       | Status filter                  |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Headers:** `Content-Disposition: attachment; filename=report_export_YYYYMMDD_HHMMSS.xlsx`
- **Body:** Binary Excel file content

**Error Responses:**

| Status Code | Description                     | Response Body                                                                                  |
| ----------- | ------------------------------- | ---------------------------------------------------------------------------------------------- |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "Invalid date format", "data": null}`             |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Failed to generate report Excel", "data": null}` |

## Common Error Responses

All endpoints may return the following common error responses:

| Status Code | Description           | When it occurs                                                            |
| ----------- | --------------------- | ------------------------------------------------------------------------- |
| 400         | Bad Request           | Invalid input parameters, validation errors, malformed request body       |
| 401         | Unauthorized          | Authentication required but not provided or invalid                       |
| 403         | Forbidden             | User does not have sufficient permissions for the requested operation     |
| 404         | Not Found             | Requested resource (submission, file, task) does not exist                |
| 500         | Internal Server Error | Unexpected server-side errors, database issues, external service failures |

### Error Response Format

All error responses follow this standard format:

```json
{
  "code": 400,
  "status": "ERROR",
  "message": "Detailed error description",
  "data": null
}
```

## Data Types and Formats

### Date Formats

- **Date fields**: Use `yyyy-MM-dd` format (e.g., "2024-01-15")
- **DateTime fields**: Use ISO 8601 format (e.g., "2024-01-15T10:30:00Z")

### Payment Types

Valid payment types include:

- Bonus Staggered
- Retention Bonus
- Token
- Performance Staggered
- Salary Adjustment
- Promotion
- Retention Salary

### Employee Grades

Valid grades: U1, U2, U3, U4, U5, U6, U7, U8, U9, U10, U11

### Status Values

Common status values:

- Waiting for Approval
- Approved
- Rejected
- Need Revision
- Waiting for Payment
- Paid
- Cancelled

### Action Values

Valid action values for approval processing:

- APPROVE
- REJECT
- REVISE

**Endpoint:** `POST /api/staggered/action`

**Content-Type:** `application/json`

**Request Body:**

```json
{
  "taskNames": ["TASK-001", "TASK-002", "TASK-003"],
  "action": "APPROVE",
  "reason": "Payment approved after verification"
}
```

**Request Body Validation:**

| Field       | Type         | Required | Description                                      |
| ----------- | ------------ | -------- | ------------------------------------------------ |
| `taskNames` | List<String> | Yes      | List of task names to process                    |
| `action`    | String       | Yes      | Action to perform: "APPROVE", "REJECT", "REVISE" |
| `reason`    | String       | No       | Reason for the action                            |

**Success Response:**

- **Status Code:** 202 Accepted
- **Content-Type:** `application/json`

```json
{
  "code": 202,
  "status": "ACCEPTED",
  "message": "Action request accepted. Processing 3 task(s) with action 'APPROVE' in the background."
}
```

**Error Responses:**

| Status Code | Description                     | Response Body                                                                             |
| ----------- | ------------------------------- | ----------------------------------------------------------------------------------------- |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "Task names are required", "data": null}`    |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Failed to start processing", "data": null}` |

**Success Response:**

- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Successfully updated 2 payment(s). Failed to update 0 payment(s).",
  "data": {
    "SP-1234ABCD": "Success",
    "SP-5678EFGH": "Success"
  }
}
```

**Error Responses:**

| Status Code | Description                     | Response Body                                                                         |
| ----------- | ------------------------------- | ------------------------------------------------------------------------------------- |
| 400         | Bad Request - Validation errors | `{"code": 400, "status": "ERROR", "message": "Invalid request format", "data": null}` |
| 500         | Internal Server Error           | `{"code": 500, "status": "ERROR", "message": "Server error occurred", "data": null}`  |
