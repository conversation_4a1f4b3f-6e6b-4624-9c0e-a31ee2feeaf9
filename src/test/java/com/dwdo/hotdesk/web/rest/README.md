# StaggeredPaymentController Unit Tests

This document describes the comprehensive unit tests for the StaggeredPaymentController endpoints including validation endpoints (`/bulk-validate` and `/single-validate`) and template endpoints (`/template-submission` and `/template-payment`).

## Test Overview

The test suite consists of **1 consolidated test class** with **38 total tests**:

**`StaggeredPaymentControllerTest`** (38 tests) - Comprehensive tests for all StaggeredPaymentController endpoints organized in nested test classes:

- **BulkValidateTests** (8 tests) - Tests for the `/api/staggered/bulk-validate` endpoint
- **SingleValidateTests** (5 tests) - Tests for the `/api/staggered/single-validate` endpoint
- **TemplateSubmissionTests** (7 tests) - Tests for the `/api/staggered/template-submission` endpoint
- **TemplatePaymentTests** (6 tests) - Tests for the `/api/staggered/template-payment` endpoint
- **SubmissionHistoryTests** (6 tests) - Tests for the `/api/staggered/submission/history` endpoint
- **ApprovalTaskListTests** (6 tests) - Tests for the `/api/staggered/list-approval` endpoint

These tests ensure that all endpoints work correctly under various scenarios including success cases, error handling, and edge cases.

## Test Coverage

### BulkValidateTests (8 tests)

Tests for `/api/staggered/bulk-validate` endpoint:

1. **testBulkValidate_Success_SingleRecord** - Tests successful validation of Excel file with single payment record
2. **testBulkValidate_Success_MultipleRecords** - Tests successful validation of Excel file with multiple payment records
3. **testBulkValidate_InvalidFileType** - Tests rejection of non-Excel files (e.g., .txt files)
4. **testBulkValidate_CorruptedFile** - Tests handling of corrupted Excel files
5. **testBulkValidate_EmptyFile** - Tests handling of empty Excel files
6. **testBulkValidate_ServiceException** - Tests handling of service exceptions during validation
7. **testBulkValidate_MissingFile** - Tests handling when no file parameter is provided
8. **testBulkValidate_ServiceCallVerification** - Tests that service method is called exactly once

### SingleValidateTests (5 tests)

Tests for `/api/staggered/single-validate` endpoint:

1. **testSingleValidate_Success** - Tests successful validation of single payment with valid data
2. **testSingleValidate_InvalidEmployee** - Tests validation with invalid employee data (NIP not found)
3. **testSingleValidate_InvalidNip** - Tests handling of invalid NIP format
4. **testSingleValidate_MissingRequiredFields** - Tests validation error when required fields are missing
5. **testSingleValidate_NullAmount** - Tests validation error when amount is null

### TemplateSubmissionTests (7 tests)

Tests for `/api/staggered/template-submission` endpoint:

1. **testGetTemplateFile_Success** - Tests successful template file generation and download
2. **testGetTemplateFile_ServiceException** - Tests handling of service exceptions during template generation
3. **testGetTemplateFile_NullResponse** - Tests handling when service returns null
4. **testGetTemplateFile_DifferentContentType** - Tests templates with different content types (XLS vs XLSX)
5. **testGetTemplateFile_LargeFile** - Tests handling of large template files
6. **testGetTemplateFile_CustomHeaders** - Tests templates with custom HTTP headers
7. **testGetTemplateFile_ServiceCallVerification** - Tests that service method is called exactly once

### TemplatePaymentTests (6 tests)

Tests for `/api/staggered/template-payment` endpoint:

1. **testGetPaymentTemplate_Success** - Tests successful payment template generation and download
2. **testGetPaymentTemplate_ServiceException** - Tests handling of service exceptions during template generation
3. **testGetPaymentTemplate_NullResponse** - Tests handling when service returns null
4. **testGetPaymentTemplate_DifferentFormat** - Tests templates with different formats (CSV, Excel)
5. **testGetPaymentTemplate_SpecialCharacters** - Tests templates with special characters in content
6. **testGetPaymentTemplate_ServiceCallVerification** - Tests that service method is called exactly once

### SubmissionHistoryTests (6 tests)

Tests for `/api/staggered/submission/history` endpoint:

1. **testGetSubmissionHistory_Success_DefaultParams** - Tests successful retrieval with default pagination parameters
2. **testGetSubmissionHistory_Success_WithFilters** - Tests retrieval with filtering parameters (status, dates, NIP, name)
3. **testGetSubmissionHistory_EmptyResults** - Tests handling of empty submission history results
4. **testGetSubmissionHistory_ServiceException** - Tests handling of service exceptions during retrieval
5. **testGetSubmissionHistory_InvalidDateFormat** - Tests handling of invalid date format parameters
6. **testGetSubmissionHistory_LargePageSize** - Tests handling of large page size requests
7. **testGetSubmissionHistory_ServiceCallVerification** - Tests that service method is called exactly once

### ApprovalTaskListTests (6 tests)

Tests for `/api/staggered/list-approval` endpoint:

1. **testGetApprovalTaskList_Success_DefaultParams** - Tests successful retrieval with default pagination parameters
2. **testGetApprovalTaskList_Success_WithFilters** - Tests retrieval with filtering parameters (status, dates, NIP, name)
3. **testGetApprovalTaskList_EmptyResults** - Tests handling of empty approval task list results
4. **testGetApprovalTaskList_ServiceException** - Tests handling of service exceptions during retrieval
5. **testGetApprovalTaskList_InvalidDateFormat** - Tests handling of invalid date format parameters
6. **testGetApprovalTaskList_LargePageSize** - Tests handling of large page size requests
7. **testGetApprovalTaskList_ServiceCallVerification** - Tests that service method is called exactly once

## Test Framework and Dependencies

- **JUnit 5** - Main testing framework
- **Mockito** - Mocking framework for service dependencies
- **Spring Test** - MockMvc for web layer testing
- **Jackson** - JSON serialization/deserialization for request/response testing

## Key Testing Patterns

### Mocking Strategy

- The `PaymentValidationService` is mocked to isolate controller logic
- Mock responses are configured to simulate various service behaviors
- Verification ensures the service methods are called with correct parameters

### Request/Response Testing

- Uses MockMvc to simulate HTTP requests
- Tests both successful responses and error scenarios
- Validates JSON response structure and content
- Checks HTTP status codes and content types

### Validation Testing

- Tests Spring Boot validation annotations (`@Valid`, `@NotBlank`, `@NotNull`)
- Ensures validation errors are properly handled
- Verifies that invalid requests don't reach the service layer

## Running the Tests

### Prerequisites

The project has `skipTests=true` configured in the Maven Surefire plugin by default. To run these tests, you need to override this setting.

### Command Line Execution

```bash
# Run individual controller test classes
mvn surefire:test -Dtest=BulkValidateControllerTest
mvn surefire:test -Dtest=SingleValidateControllerTest
mvn surefire:test -Dtest=TemplateSubmissionControllerTest
mvn surefire:test -Dtest=TemplatePaymentControllerTest

# Run all controller tests
mvn surefire:test -Dtest=*ControllerTest

# Run a specific test method
mvn surefire:test -Dtest=BulkValidateControllerTest#testValidateExcelFile_Success

# Run all tests with skipTests disabled (temporary override)
mvn test -DskipTests=false
```

### IDE Execution

Most IDEs (IntelliJ IDEA, Eclipse, VS Code) can run these tests directly:

1. Right-click on the test class or individual test methods
2. Select "Run Test" or "Debug Test"
3. The IDE will handle the Maven configuration automatically

## Test Data Examples

### Valid Single Payment Request

```json
{
  "nip": "123456789",
  "name": "Jane Smith",
  "grade": "B",
  "paymentType": "Bonus",
  "amount": 2000000,
  "description": "Performance bonus",
  "monthOfProcess": "February",
  "yearOfProcess": "2024"
}
```

### Expected Success Response

```json
{
  "code": 200,
  "status": "OK",
  "message": "Payment data validated successfully",
  "data": {
    "nip": "123456789",
    "name": "Jane Smith",
    "grade": "B",
    "paymentType": "Bonus",
    "amount": 2000000,
    "description": "Performance bonus",
    "monthOfProcess": "February",
    "yearOfProcess": "2024",
    "nipValid": "NIP is valid",
    "directorate": "HR",
    "eligible": true,
    "slik": "-",
    "sanction": "-",
    "terminationDate": "-"
  }
}
```

## Error Scenarios Tested

1. **File Type Validation** - Only Excel files (.xlsx, .xls) are accepted
2. **Required Field Validation** - All mandatory fields must be provided
3. **Data Type Validation** - Amount must be a valid BigDecimal
4. **Business Logic Validation** - NIP validation through external service
5. **Service Error Handling** - Proper error responses for service failures

## Assertions and Verifications

Each test includes comprehensive assertions:

- HTTP status code verification
- Response content type validation
- JSON structure and value assertions
- Service method invocation verification
- Error message validation

## Best Practices Demonstrated

1. **Descriptive Test Names** - Each test method clearly describes what it tests
2. **Arrange-Act-Assert Pattern** - Tests follow the AAA pattern for clarity
3. **Isolated Testing** - Each test is independent and doesn't affect others
4. **Comprehensive Coverage** - Tests cover both happy path and error scenarios
5. **Realistic Test Data** - Uses realistic payment data for testing
6. **Proper Mocking** - Mocks external dependencies appropriately
7. **Detailed Documentation** - Each test is well-documented with @DisplayName annotations

## Maintenance Notes

- Update test data when business rules change
- Add new test cases when new validation rules are introduced
- Keep mock responses in sync with actual service behavior
- Review and update error scenarios as the application evolves
