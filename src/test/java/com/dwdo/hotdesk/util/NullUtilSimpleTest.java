package com.dwdo.hotdesk.util;

import java.time.LocalDate;

/**
 * Simple test class to verify NullUtil functionality without JUnit dependencies
 */
public class NullUtilSimpleTest {
    
    public static void main(String[] args) {
        System.out.println("Testing NullUtil...");
        
        // Test null string
        String result1 = NullUtil.toDisplayString((String) null);
        System.out.println("Null string: " + result1 + " (expected: -)");
        
        // Test empty string
        String result2 = NullUtil.toDisplayString("");
        System.out.println("Empty string: " + result2 + " (expected: -)");
        
        // Test whitespace string
        String result3 = NullUtil.toDisplayString("   ");
        System.out.println("Whitespace string: " + result3 + " (expected: -)");
        
        // Test valid string
        String result4 = NullUtil.toDisplayString("Test remarks");
        System.out.println("Valid string: " + result4 + " (expected: Test remarks)");
        
        // Test null LocalDate
        String result5 = NullUtil.toDisplayString((LocalDate) null);
        System.out.println("Null LocalDate: " + result5 + " (expected: -)");
        
        // Test valid LocalDate
        String result6 = NullUtil.toDisplayString(LocalDate.of(2024, 1, 15));
        System.out.println("Valid LocalDate: " + result6 + " (expected: 2024-01-15)");
        
        // Test null object
        String result7 = NullUtil.toDisplayString((Object) null);
        System.out.println("Null object: " + result7 + " (expected: -)");
        
        // Test isNullOrEmpty
        boolean result8 = NullUtil.isNullOrEmpty(null);
        System.out.println("isNullOrEmpty(null): " + result8 + " (expected: true)");
        
        boolean result9 = NullUtil.isNullOrEmpty("Test");
        System.out.println("isNullOrEmpty('Test'): " + result9 + " (expected: false)");
        
        // Test getNullDisplayValue
        String result10 = NullUtil.getNullDisplayValue();
        System.out.println("getNullDisplayValue(): " + result10 + " (expected: -)");
        
        System.out.println("NullUtil testing completed!");
    }
}
