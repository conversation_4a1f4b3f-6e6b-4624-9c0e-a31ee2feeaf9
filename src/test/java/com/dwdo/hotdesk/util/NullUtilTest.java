package com.dwdo.hotdesk.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("NullUtil Tests")
class NullUtilTest {

    @Test
    @DisplayName("Should convert null string to '-'")
    void testToDisplayString_NullString() {
        // When
        String result = NullUtil.toDisplayString((String) null);
        
        // Then
        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Should convert empty string to '-'")
    void testToDisplayString_EmptyString() {
        // When
        String result = NullUtil.toDisplayString("");
        
        // Then
        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Should convert whitespace string to '-'")
    void testToDisplayString_WhitespaceString() {
        // When
        String result = NullUtil.toDisplayString("   ");
        
        // Then
        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Should return original string when not null/empty")
    void testToDisplayString_ValidString() {
        // Given
        String input = "Test remarks";
        
        // When
        String result = NullUtil.toDisplayString(input);
        
        // Then
        assertThat(result).isEqualTo("Test remarks");
    }

    @Test
    @DisplayName("Should convert null LocalDate to '-'")
    void testToDisplayString_NullLocalDate() {
        // When
        String result = NullUtil.toDisplayString((LocalDate) null);
        
        // Then
        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Should convert LocalDate to string representation")
    void testToDisplayString_ValidLocalDate() {
        // Given
        LocalDate date = LocalDate.of(2024, 1, 15);
        
        // When
        String result = NullUtil.toDisplayString(date);
        
        // Then
        assertThat(result).isEqualTo("2024-01-15");
    }

    @Test
    @DisplayName("Should convert null object to '-'")
    void testToDisplayString_NullObject() {
        // When
        String result = NullUtil.toDisplayString((Object) null);
        
        // Then
        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Should convert object to string representation")
    void testToDisplayString_ValidObject() {
        // Given
        BigDecimal amount = new BigDecimal("5000000");
        
        // When
        String result = NullUtil.toDisplayString(amount);
        
        // Then
        assertThat(result).isEqualTo("5000000");
    }

    @Test
    @DisplayName("Should return true for null string")
    void testIsNullOrEmpty_NullString() {
        // When
        boolean result = NullUtil.isNullOrEmpty(null);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("Should return true for empty string")
    void testIsNullOrEmpty_EmptyString() {
        // When
        boolean result = NullUtil.isNullOrEmpty("");
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("Should return true for whitespace string")
    void testIsNullOrEmpty_WhitespaceString() {
        // When
        boolean result = NullUtil.isNullOrEmpty("   ");
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("Should return false for valid string")
    void testIsNullOrEmpty_ValidString() {
        // When
        boolean result = NullUtil.isNullOrEmpty("Test");
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("Should return correct null display value")
    void testGetNullDisplayValue() {
        // When
        String result = NullUtil.getNullDisplayValue();
        
        // Then
        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Should handle string with only tabs and spaces")
    void testToDisplayString_TabsAndSpaces() {
        // When
        String result = NullUtil.toDisplayString("\t  \n  ");
        
        // Then
        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Should preserve string with actual content and whitespace")
    void testToDisplayString_ContentWithWhitespace() {
        // Given
        String input = "  Test content  ";
        
        // When
        String result = NullUtil.toDisplayString(input);
        
        // Then
        assertThat(result).isEqualTo("  Test content  ");
    }
}
