package com.dwdo.hotdesk.service;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Date Format Tests")
class DateFormatTest {

    @Test
    @DisplayName("Should create date with no time components")
    void testDateWithoutTime() {
        // Given
        String monthOfProcess = "January";
        String yearOfProcess = "2024";

        // When
        Date result = parseEffectiveDate(monthOfProcess, yearOfProcess);

        // Then
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(result);
        
        assertThat(calendar.get(Calendar.HOUR_OF_DAY)).isEqualTo(0);
        assertThat(calendar.get(Calendar.MINUTE)).isEqualTo(0);
        assertThat(calendar.get(Calendar.SECOND)).isEqualTo(0);
        assertThat(calendar.get(Calendar.MILLISECOND)).isEqualTo(0);
        
        assertThat(calendar.get(Calendar.YEAR)).isEqualTo(2024);
        assertThat(calendar.get(Calendar.MONTH)).isEqualTo(Calendar.JANUARY);
        assertThat(calendar.get(Calendar.DAY_OF_MONTH)).isEqualTo(1);
    }

    @Test
    @DisplayName("Should handle different months correctly")
    void testDifferentMonths() {
        // Test February
        Date febDate = parseEffectiveDate("February", "2024");
        Calendar febCal = Calendar.getInstance();
        febCal.setTime(febDate);
        assertThat(febCal.get(Calendar.MONTH)).isEqualTo(Calendar.FEBRUARY);
        assertThat(febCal.get(Calendar.HOUR_OF_DAY)).isEqualTo(0);

        // Test December
        Date decDate = parseEffectiveDate("December", "2024");
        Calendar decCal = Calendar.getInstance();
        decCal.setTime(decDate);
        assertThat(decCal.get(Calendar.MONTH)).isEqualTo(Calendar.DECEMBER);
        assertThat(decCal.get(Calendar.HOUR_OF_DAY)).isEqualTo(0);
    }

    // Helper method that mimics the GeneratePaymentService logic
    private Date parseEffectiveDate(String monthOfProcess, String yearOfProcess) {
        try {
            Calendar calendar = Calendar.getInstance();
            
            // Set the date components
            calendar.set(Calendar.YEAR, Integer.parseInt(yearOfProcess));
            calendar.set(Calendar.MONTH, getMonthNumber(monthOfProcess));
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            
            // Clear time components to ensure only date is used (no time)
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            return calendar.getTime();
        } catch (Exception e) {
            // Even for fallback, ensure we return a date with no time components
            Calendar fallbackCalendar = Calendar.getInstance();
            fallbackCalendar.set(Calendar.HOUR_OF_DAY, 0);
            fallbackCalendar.set(Calendar.MINUTE, 0);
            fallbackCalendar.set(Calendar.SECOND, 0);
            fallbackCalendar.set(Calendar.MILLISECOND, 0);
            return fallbackCalendar.getTime();
        }
    }

    private int getMonthNumber(String monthName) {
        if (monthName == null || monthName.trim().isEmpty()) {
            return 0;
        }

        String month = monthName.trim().toLowerCase();
        return switch (month) {
            case "january" -> 0;
            case "february" -> 1;
            case "march" -> 2;
            case "april" -> 3;
            case "may" -> 4;
            case "june" -> 5;
            case "july" -> 6;
            case "august" -> 7;
            case "september" -> 8;
            case "october" -> 9;
            case "november" -> 10;
            case "december" -> 11;
            default -> 0;
        };
    }
}
