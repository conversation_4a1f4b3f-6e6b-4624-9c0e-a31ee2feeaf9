package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.SubmissionDetailDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.ApprovalClient;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("SubmissionDetailService Tests")
class SubmissionDetailServiceTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private ApprovalClient approvalClient;

    @InjectMocks
    private SubmissionDetailService submissionDetailService;

    private Submission mockSubmission;
    private static final Long SUBMISSION_ID = 1L;
    private static final String USER_NIP = "123456789";
    private static final List<String> ADMIN_ROLES = Arrays.asList("ROLE_ADMIN_HRPAYROLL");
    private static final List<String> HRBP_ROLES = Arrays.asList("ROLE_ADMIN_HRBP");
    private static final List<String> HRREWARD_ROLES = Arrays.asList("ROLE_ADMIN_HRREWARD");
    private static final List<String> USER_ROLES = Arrays.asList("ROLE_USER");

    @BeforeEach
    void setUp() {
        mockSubmission = createMockSubmission();
    }

    @Nested
    @DisplayName("Get Submission Detail Tests")
    class GetSubmissionDetailTests {

        @Test
        @DisplayName("Should successfully retrieve submission detail for admin user")
        void testGetSubmissionDetail_Success_AdminUser() {
            // Given
            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of("admin123"));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(ADMIN_ROLES);

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));

                ApiResponse mockApiResponse = new ApiResponse();
                mockApiResponse.setSuccess(true);
                mockApiResponse.setData(new Object()); // Mock data object

                when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

                // When
                SubmissionDetailDTO result = submissionDetailService.getSubmissionDetail(SUBMISSION_ID);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getId()).isEqualTo(SUBMISSION_ID);
                assertThat(result.getCreatedBy()).isEqualTo(USER_NIP);
                assertThat(result.getReferenceNumber()).isEqualTo("REF-001");
                assertThat(result.getTaskName()).isEqualTo("TASK-001");

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verify(approvalClient).detail("TASK-001");
            }
        }

        @Test
        @DisplayName("Should successfully retrieve submission detail for HRBP user accessing HRBP submission")
        void testGetSubmissionDetail_Success_HRBPUser() {
            // Given
            mockSubmission.setSubmitterJob("HRBP");

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of("hrbp_user"));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(HRBP_ROLES);

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));

                ApiResponse mockApiResponse = new ApiResponse();
                mockApiResponse.setSuccess(true);
                mockApiResponse.setData(new Object());

                when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

                // When
                SubmissionDetailDTO result = submissionDetailService.getSubmissionDetail(SUBMISSION_ID);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getId()).isEqualTo(SUBMISSION_ID);
                assertThat(result.getCreatedBy()).isEqualTo(USER_NIP);

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verify(approvalClient).detail("TASK-001");
            }
        }

        @Test
        @DisplayName("Should successfully retrieve submission detail for HRREWARD user accessing HRREWARD submission")
        void testGetSubmissionDetail_Success_HRREWARDUser() {
            // Given
            mockSubmission.setSubmitterJob("HRREWARD");

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of("hrreward_user"));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(HRREWARD_ROLES);

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));

                ApiResponse mockApiResponse = new ApiResponse();
                mockApiResponse.setSuccess(true);
                mockApiResponse.setData(new Object());

                when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

                // When
                SubmissionDetailDTO result = submissionDetailService.getSubmissionDetail(SUBMISSION_ID);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getId()).isEqualTo(SUBMISSION_ID);
                assertThat(result.getCreatedBy()).isEqualTo(USER_NIP);

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verify(approvalClient).detail("TASK-001");
            }
        }

        @Test
        @DisplayName("Should throw exception when submission not found")
        void testGetSubmissionDetail_SubmissionNotFound() {
            // Given
            when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> submissionDetailService.getSubmissionDetail(SUBMISSION_ID))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessage("Submission not found with ID: " + SUBMISSION_ID);

            verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
            verifyNoInteractions(approvalClient);
        }

        @Test
        @DisplayName("Should throw exception when HRBP user tries to access HRREWARD submission")
        void testGetSubmissionDetail_AccessDenied_HRBPToHRREWARD() {
            // Given
            mockSubmission.setSubmitterJob("HRREWARD");

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of("hrbp_user"));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(HRBP_ROLES);

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));

                // When & Then
                assertThatThrownBy(() -> submissionDetailService.getSubmissionDetail(SUBMISSION_ID))
                        .isInstanceOf(CustomBadRequestException.class)
                        .hasMessage("Access denied. HRBP users can only view HRBP submissions.");

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verifyNoInteractions(approvalClient);
            }
        }

        @Test
        @DisplayName("Should successfully retrieve submission detail for owner user")
        void testGetSubmissionDetail_Success_OwnerUser() {
            // Given
            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(USER_NIP));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(USER_ROLES);

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));

                ApiResponse mockApiResponse = new ApiResponse();
                mockApiResponse.setSuccess(true);
                mockApiResponse.setData(new Object());

                when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

                // When
                SubmissionDetailDTO result = submissionDetailService.getSubmissionDetail(SUBMISSION_ID);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getId()).isEqualTo(SUBMISSION_ID);
                assertThat(result.getCreatedBy()).isEqualTo(USER_NIP);

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verify(approvalClient).detail("TASK-001");
            }
        }

        @Test
        @DisplayName("Should handle approval client error gracefully")
        void testGetSubmissionDetail_ApprovalClientError() {
            // Given
            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(USER_NIP));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(USER_ROLES);

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));
                when(approvalClient.detail(anyString())).thenThrow(new RuntimeException("API Error"));

                // When
                SubmissionDetailDTO result = submissionDetailService.getSubmissionDetail(SUBMISSION_ID);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getId()).isEqualTo(SUBMISSION_ID);
                assertThat(result.getCurrentReviewer()).isEqualTo(""); // Original value from submission

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verify(approvalClient).detail("TASK-001");
            }
        }

        @Test
        @DisplayName("Should grant access to ROLE_ADMIN_HRPAYROLL_HEAD")
        void testGetSubmissionDetail_Success_AdminHeadRole() {
            // Given
            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of("admin_head"));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(Arrays.asList("ROLE_ADMIN_HRPAYROLL_HEAD"));

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));

                ApiResponse mockApiResponse = new ApiResponse();
                mockApiResponse.setSuccess(true);
                mockApiResponse.setData(new Object());

                when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

                // When
                SubmissionDetailDTO result = submissionDetailService.getSubmissionDetail(SUBMISSION_ID);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getId()).isEqualTo(SUBMISSION_ID);

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verify(approvalClient).detail("TASK-001");
            }
        }

        @Test
        @DisplayName("Should handle submission without task name")
        void testGetSubmissionDetail_NoTaskName() {
            // Given
            mockSubmission.setTaskName(null);

            try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
                securityUtilMock.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(USER_NIP));
                securityUtilMock.when(SecurityUtil::getCurrentUserRole).thenReturn(USER_ROLES);

                when(submissionRepository.findByIdWithSupportingFiles(SUBMISSION_ID)).thenReturn(Optional.of(mockSubmission));

                // When
                SubmissionDetailDTO result = submissionDetailService.getSubmissionDetail(SUBMISSION_ID);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getId()).isEqualTo(SUBMISSION_ID);
                assertThat(result.getTaskName()).isNull();

                verify(submissionRepository).findByIdWithSupportingFiles(SUBMISSION_ID);
                verifyNoInteractions(approvalClient);
            }
        }
    }

    /**
     * Helper method to create a mock Submission entity
     */
    private Submission createMockSubmission() {
        Submission submission = new Submission();
        submission.setId(SUBMISSION_ID);
        submission.setTaskName("TASK-001");
        submission.setReferenceNumber("REF-001");
        submission.setCreatedBy(USER_NIP);
        submission.setSubmitterName("John Doe");
        submission.setSubmitterJob("Developer");
        submission.setStatus("PENDING");
        submission.setNip("123456789");
        submission.setName("John Doe");
        submission.setGrade("A");
        submission.setPaymentType("Salary");
        submission.setAmount(new BigDecimal("5000000"));
        submission.setDescription("Monthly salary");
        submission.setMonthOfProcess("January");
        submission.setYearOfProcess("2024");
        submission.setDirectorate("IT");
        submission.setSlik("-");
        submission.setSanction("-");
        submission.setTerminationDate("-");
        submission.setEligible(true);
        submission.setCurrentReviewer("");
        return submission;
    }

    @Nested
    @DisplayName("Transaction Console Log Tests")
    class TransactionConsoleLogTests {

        @Test
        @DisplayName("Should successfully retrieve transaction console log")
        void testGetTransactionConsoleLog_Success() {
            // Given
            String taskName = "TASK-001";
            ApiResponse mockResponse = new ApiResponse();
            mockResponse.setSuccess(true);
            mockResponse.setMessage("Success");
            mockResponse.setData("Transaction log data");

            when(approvalClient.detail(taskName)).thenReturn(mockResponse);

            // When
            ApiResponse result = submissionDetailService.getTransactionConsoleLog(taskName);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getMessage()).isEqualTo("Success");
            assertThat(result.getData()).isEqualTo("Transaction log data");

            verify(approvalClient).detail(taskName);
        }

        @Test
        @DisplayName("Should throw exception when approval client returns null")
        void testGetTransactionConsoleLog_NullResponse() {
            // Given
            String taskName = "TASK-001";
            when(approvalClient.detail(taskName)).thenReturn(null);

            // When & Then
            assertThatThrownBy(() -> submissionDetailService.getTransactionConsoleLog(taskName))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("No response received from transaction console service for task: " + taskName);

            verify(approvalClient).detail(taskName);
        }

        @Test
        @DisplayName("Should throw exception when approval client returns unsuccessful response")
        void testGetTransactionConsoleLog_UnsuccessfulResponse() {
            // Given
            String taskName = "TASK-001";
            ApiResponse mockResponse = new ApiResponse();
            mockResponse.setSuccess(false);
            mockResponse.setMessage("Service error");

            when(approvalClient.detail(taskName)).thenReturn(mockResponse);

            // When & Then
            assertThatThrownBy(() -> submissionDetailService.getTransactionConsoleLog(taskName))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Transaction console service error: Service error");

            verify(approvalClient).detail(taskName);
        }

        @Test
        @DisplayName("Should throw exception when approval client throws exception")
        void testGetTransactionConsoleLog_ClientException() {
            // Given
            String taskName = "TASK-001";
            when(approvalClient.detail(taskName)).thenThrow(new RuntimeException("Connection error"));

            // When & Then
            assertThatThrownBy(() -> submissionDetailService.getTransactionConsoleLog(taskName))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Failed to retrieve transaction console log for task: " + taskName);

            verify(approvalClient).detail(taskName);
        }
    }
}
