package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for PaymentValidationService utility methods
 * Tests month normalization, year validation, and edge cases
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Payment Validation Service Utility Tests")
class PaymentValidationServiceUtilityTest {

    @Mock
    private EmployeeClient employeeClient;

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @InjectMocks
    private PaymentValidationService paymentValidationService;

    private EpiccEmployeeProfileImpl sampleEmployeeProfile;

    @BeforeEach
    void setUp() {
        // Inject the mocks using reflection since they're @Autowired
        ReflectionTestUtils.setField(paymentValidationService, "employeeClient", employeeClient);
        ReflectionTestUtils.setField(paymentValidationService, "submissionRepository", submissionRepository);

        // Setup sample employee profile
        sampleEmployeeProfile = new EpiccEmployeeProfileImpl();
        sampleEmployeeProfile.setDirectorate("IT");
        sampleEmployeeProfile.setTerminationDate(null);
    }

    @Test
    @DisplayName("Should normalize numeric months correctly")
    void testMonthNormalization_NumericMonths() {
        // Given
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        String[][] numericMonths = {
            {"1", "January"}, {"2", "February"}, {"3", "March"}, {"4", "April"},
            {"5", "May"}, {"6", "June"}, {"7", "July"}, {"8", "August"},
            {"9", "September"}, {"10", "October"}, {"11", "November"}, {"12", "December"}
        };

        for (String[] monthPair : numericMonths) {
            SingleValidRequestDTO request = createValidRequest("123456789", monthPair[0], "2024");

            // When
            GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

            // Then
            PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
            assertEquals(monthPair[1], paymentData.getMonthOfProcess(),
                    "Month " + monthPair[0] + " should be normalized to " + monthPair[1]);
        }
    }

    @Test
    @DisplayName("Should normalize English month abbreviations correctly")
    void testMonthNormalization_EnglishAbbreviations() {
        // Given
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        String[][] englishAbbreviations = {
            {"jan", "January"}, {"feb", "February"}, {"mar", "March"}, {"apr", "April"},
            {"may", "May"}, {"jun", "June"}, {"jul", "July"}, {"aug", "August"},
            {"sep", "September"}, {"sept", "September"}, {"oct", "October"}, 
            {"nov", "November"}, {"dec", "December"}
        };

        for (String[] monthPair : englishAbbreviations) {
            SingleValidRequestDTO request = createValidRequest("123456789", monthPair[0], "2024");

            // When
            GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

            // Then
            PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
            assertEquals(monthPair[1], paymentData.getMonthOfProcess(),
                    "Month " + monthPair[0] + " should be normalized to " + monthPair[1]);
        }
    }

    @Test
    @DisplayName("Should normalize full English month names correctly")
    void testMonthNormalization_FullEnglishNames() {
        // Given
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        String[][] fullEnglishNames = {
            {"january", "January"}, {"february", "February"}, {"march", "March"}, {"april", "April"},
            {"may", "May"}, {"june", "June"}, {"july", "July"}, {"august", "August"},
            {"september", "September"}, {"october", "October"}, {"november", "November"}, {"december", "December"}
        };

        for (String[] monthPair : fullEnglishNames) {
            SingleValidRequestDTO request = createValidRequest("123456789", monthPair[0], "2024");

            // When
            GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

            // Then
            PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
            assertEquals(monthPair[1], paymentData.getMonthOfProcess(),
                    "Month " + monthPair[0] + " should be normalized to " + monthPair[1]);
        }
    }

    @Test
    @DisplayName("Should handle invalid month values gracefully")
    void testMonthNormalization_InvalidMonths() {
        // Given
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        String[] invalidMonths = {"13", "0", "-1", "invalid", "month13", ""};

        for (String invalidMonth : invalidMonths) {
            SingleValidRequestDTO request = createValidRequest("123456789", invalidMonth, "2024");

            // When
            GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

            // Then
            PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
            assertEquals(invalidMonth, paymentData.getMonthOfProcess(),
                    "Invalid month " + invalidMonth + " should remain unchanged");
        }
    }

    @Test
    @DisplayName("Should normalize year values correctly")
    void testYearNormalization_ValidYears() {
        // Given
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        String[] validYears = {"2020", "2024", "2050", "2100", " 2024 ", "  2025  "};

        for (String year : validYears) {
            SingleValidRequestDTO request = createValidRequest("123456789", "January", year);

            // When
            GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

            // Then
            PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
            assertEquals(year.trim(), paymentData.getYearOfProcess(),
                    "Year " + year + " should be normalized to " + year.trim());
        }
    }

    @Test
    @DisplayName("Should extract year from text correctly")
    void testYearNormalization_YearExtraction() {
        // Given
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        String[][] yearExtractionTests = {
            {"Year 2024 Process", "2024"},
            {"Payment for 2025", "2025"},
            {"2023-2024 Period", "2023"},
            {"Process 2026 Bonus", "2026"},
            {"Salary 2027", "2027"}
        };

        for (String[] yearTest : yearExtractionTests) {
            SingleValidRequestDTO request = createValidRequest("123456789", "January", yearTest[0]);

            // When
            GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

            // Then
            PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
            assertEquals(yearTest[1], paymentData.getYearOfProcess(),
                    "Year should be extracted from '" + yearTest[0] + "' as " + yearTest[1]);
        }
    }

    @Test
    @DisplayName("Should validate year range boundaries")
    void testYearValidation_BoundaryValues() {
        // Test valid boundary years
        String[] validBoundaryYears = {"2020", "2100"};
        
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        for (String year : validBoundaryYears) {
            SingleValidRequestDTO request = createValidRequest("123456789", "January", year);

            // Should not throw exception
            assertDoesNotThrow(() -> {
                GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);
                PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
                assertEquals(year, paymentData.getYearOfProcess());
            });
        }

        // Test invalid boundary years
        String[] invalidBoundaryYears = {"2019", "2101"};

        for (String year : invalidBoundaryYears) {
            SingleValidRequestDTO request = createValidRequest("123456789", "January", year);

            // Should throw exception
            CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
                paymentValidationService.validateSinglePayment(request);
            });

            assertTrue(exception.getMessage().contains("Year of Process must be between 2020 and 2100"));
        }
    }

    @Test
    @DisplayName("Should handle null and empty values gracefully")
    void testNullAndEmptyValues() {
        // Test null month
        SingleValidRequestDTO requestWithNullMonth = createValidRequest("123456789", null, "2024");
        
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithNullMonth);
        });
        assertEquals("Month of Process is required", exception.getMessage());

        // Test empty year
        SingleValidRequestDTO requestWithEmptyYear = createValidRequest("123456789", "January", "");
        
        exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithEmptyYear);
        });
        assertEquals("Year of Process is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle whitespace in month and year values")
    void testWhitespaceHandling() {
        // Given
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        SingleValidRequestDTO request = createValidRequest("123456789", "  January  ", "  2024  ");

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

        // Then
        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertEquals("January", paymentData.getMonthOfProcess());
        assertEquals("2024", paymentData.getYearOfProcess());
    }

    /**
     * Helper method to create a valid request with specific month and year
     */
    private SingleValidRequestDTO createValidRequest(String nip, String month, String year) {
        return SingleValidRequestDTO.builder()
                .nip(nip)
                .name("Test Employee")
                .grade("U5")
                .paymentType("Bonus Staggered")
                .amount(new BigDecimal("1000000"))
                .description("Test bonus")
                .monthOfProcess(month)
                .yearOfProcess(year)
                .build();
    }
}
