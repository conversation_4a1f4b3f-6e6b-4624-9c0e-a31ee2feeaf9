package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.FileDTO;
import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.model.MainFile;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SupportingFiles;
import com.dwdo.hotdesk.repository.MainFileRepository;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SupportingFilesRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import com.dwdo.hotdesk.service.storage.CloudStorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SubmissionService
 * Tests both bulk and single submission functionality
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Submission Service Tests")
class SubmissionServiceTest {

    @Mock
    private PaymentValidationService validationService;

    @Mock
    private ApprovalService approvalService;

    @Mock
    private CloudStorageService storageService;

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private MainFileRepository mainFileRepository;

    @Mock
    private SupportingFilesRepository supportingFilesRepository;

    @Mock
    private EmployeeClient employeeClient;

    @InjectMocks
    private SubmissionService submissionService;

    @Mock
    private MultipartFile mainFile;

    @Mock
    private MultipartFile supportingFile;

    private PaymentDataDTO samplePaymentData;
    private SingleValidRequestDTO sampleSingleRequest;
    private EpiccEmployeeProfileImpl sampleEmployeeProfile;
    private MainFile sampleMainFile;
    private SupportingFiles sampleSupportingFile;
    private Submission sampleSubmission;

    @BeforeEach
    void setUp() {
        // Inject the employeeClient mock using reflection since it's @Autowired
        ReflectionTestUtils.setField(submissionService, "employeeClient", employeeClient);

        // Setup sample payment data
        samplePaymentData = PaymentDataDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .directorate("IT")
                .eligible(true)
                .slik("-")
                .sanction("-")
                .terminationDate("-")
                .build();

        // Setup sample single request
        sampleSingleRequest = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // Setup sample employee profile
        sampleEmployeeProfile = new EpiccEmployeeProfileImpl();
        sampleEmployeeProfile.setLastName("John Doe");
        sampleEmployeeProfile.setDirectorate("IT");

        // Setup sample main file
        sampleMainFile = MainFile.builder()
                .id(1L)
                .fileName("test.xlsx")
                .filePath("path/to/test.xlsx")
                .fileType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .fileSize(1024L)
                .build();

        // Setup sample supporting file
        sampleSupportingFile = SupportingFiles.builder()
                .id(1L)
                .fileName("support.pdf")
                .filePath("path/to/support.pdf")
                .fileType("application/pdf")
                .fileSize(512L)
                .build();

        // Setup sample submission
        sampleSubmission = Submission.builder()
                .id(1L)
                .referenceNumber("SP-12345678")
                .submitterName("John Doe")
                .submitterJob("HRBP")
                .status("Pending")
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .directorate("IT")
                .eligible(true)
                .slik("-")
                .sanction("-")
                .terminationDate("-")
                .mainFile(sampleMainFile)
                .supportingFiles(new ArrayList<>())
                .build();
    }

    @Test
    @DisplayName("Should successfully process bulk submission with valid data")
    void testBulkSubmission_Success() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);
        List<PaymentDataDTO> validatedData = Arrays.asList(samplePaymentData);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Validation successful")
                    .data(validatedData)
                    .build();
            when(validationService.validateExcelFile(mainFile))
                    .thenReturn(validationResponse);

            // Mock file operations
            when(mainFile.getOriginalFilename()).thenReturn("test.xlsx");
            when(mainFile.getContentType()).thenReturn("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            when(mainFile.getSize()).thenReturn(1024L);

            when(supportingFile.getOriginalFilename()).thenReturn("support.pdf");
            when(supportingFile.getContentType()).thenReturn("application/pdf");
            when(supportingFile.getSize()).thenReturn(512L);

            // Mock storage service
            FileDTO fileDTO = new FileDTO();
            fileDTO.setName("test.xlsx");
            when(storageService.storeFile(anyString(), eq(mainFile))).thenReturn(fileDTO);
            when(storageService.storeFile(anyString(), eq(supportingFile))).thenReturn(fileDTO);

            // Mock repositories
            when(mainFileRepository.save(any(MainFile.class))).thenReturn(sampleMainFile);
            when(supportingFilesRepository.save(any(SupportingFiles.class))).thenReturn(sampleSupportingFile);
            when(submissionRepository.save(any(Submission.class))).thenReturn(sampleSubmission);
            when(submissionRepository.countByReferenceNumber(anyString())).thenReturn(0L);

            // When
            List<GeneralBodyResponse> responses = submissionService.bulkSubmission(mainFile, supportingFiles);

            // Then
            assertNotNull(responses);
            assertEquals(1, responses.size());
            assertEquals(200, responses.get(0).getCode());
            assertEquals("OK", responses.get(0).getStatus());
            assertTrue(responses.get(0).getMessage().contains("Submission received successfully"));

            // Verify interactions
            verify(validationService).validateExcelFile(mainFile);
            verify(storageService, times(2)).storeFile(anyString(), any(MultipartFile.class));
            verify(mainFileRepository).save(any(MainFile.class));
            verify(supportingFilesRepository).save(any(SupportingFiles.class));
            verify(submissionRepository, atLeast(2)).save(any(Submission.class)); // Service saves multiple times
            verify(approvalService).updateStatus(anyList(), eq("123456789"), eq("John Doe"));
        }
    }

    @Test
    @DisplayName("Should handle bulk submission when validation fails")
    void testBulkSubmission_ValidationFails() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service to return error
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(400)
                    .status("Error")
                    .message("Validation failed")
                    .build();
            when(validationService.validateExcelFile(mainFile))
                    .thenReturn(validationResponse);

            // When
            List<GeneralBodyResponse> responses = submissionService.bulkSubmission(mainFile, supportingFiles);

            // Then
            assertNotNull(responses);
            assertTrue(responses.isEmpty());

            // Verify that no storage or repository operations were performed
            verify(storageService, never()).storeFile(anyString(), any(MultipartFile.class));
            verify(submissionRepository, never()).save(any(Submission.class));
        }
    }

    @Test
    @DisplayName("Should handle bulk submission when user is not authenticated")
    void testBulkSubmission_UserNotAuthenticated() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context to return empty
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.empty());

            // When & Then
            CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
                submissionService.bulkSubmission(mainFile, supportingFiles);
            });

            assertEquals(401, exception.getCode());
            assertEquals("Authentication Error", exception.getStatus());
            assertTrue(exception.getMessage().contains("User not authenticated"));
        }
    }

    @Test
    @DisplayName("Should handle bulk submission when user has invalid role")
    void testBulkSubmission_InvalidUserRole() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_USER")); // Invalid role

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // When & Then
            CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
                submissionService.bulkSubmission(mainFile, supportingFiles);
            });

            assertEquals(403, exception.getCode());
            assertEquals("Forbidden", exception.getStatus());
            assertTrue(exception.getMessage().contains("required role"));
        }
    }

    @Test
    @DisplayName("Should handle bulk submission when main file storage fails")
    void testBulkSubmission_MainFileStorageFails() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);
        List<PaymentDataDTO> validatedData = Arrays.asList(samplePaymentData);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Validation successful")
                    .data(validatedData)
                    .build();
            when(validationService.validateExcelFile(mainFile))
                    .thenReturn(validationResponse);

            // Mock file operations
            lenient().when(mainFile.getOriginalFilename()).thenReturn("test.xlsx");
            lenient().when(mainFile.getContentType()).thenReturn("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            lenient().when(mainFile.getSize()).thenReturn(1024L);

            // Mock storage service to fail
            when(storageService.storeFile(anyString(), eq(mainFile))).thenReturn(null);

            // When
            List<GeneralBodyResponse> responses = submissionService.bulkSubmission(mainFile, supportingFiles);

            // Then
            assertNotNull(responses);
            assertEquals(1, responses.size());
            assertEquals(500, responses.get(0).getCode());
            assertEquals("Error", responses.get(0).getStatus());
            assertTrue(responses.get(0).getMessage().contains("Failed to store main file"));

            // Verify that submission was not saved
            verify(submissionRepository, never()).save(any(Submission.class));
        }
    }

    @Test
    @DisplayName("Should successfully process single submission with valid data")
    void testSingleSubmission_Success() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Validation successful")
                    .data(samplePaymentData)
                    .build();
            when(validationService.validateSinglePayment(sampleSingleRequest))
                    .thenReturn(validationResponse);

            // Mock file operations
            when(supportingFile.getOriginalFilename()).thenReturn("support.pdf");
            when(supportingFile.getContentType()).thenReturn("application/pdf");
            when(supportingFile.getSize()).thenReturn(512L);

            // Mock storage service
            FileDTO fileDTO = new FileDTO();
            fileDTO.setName("support.pdf");
            when(storageService.storeFile(anyString(), eq(supportingFile))).thenReturn(fileDTO);

            // Mock repositories
            when(supportingFilesRepository.save(any(SupportingFiles.class))).thenReturn(sampleSupportingFile);
            when(submissionRepository.save(any(Submission.class))).thenReturn(sampleSubmission);
            when(submissionRepository.countByReferenceNumber(anyString())).thenReturn(0L);

            // When
            GeneralBodyResponse response = submissionService.singleSubmission(sampleSingleRequest, supportingFiles);

            // Then
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertEquals("OK", response.getStatus());
            assertTrue(response.getMessage().contains("Submission received successfully"));

            // Verify interactions
            verify(validationService).validateSinglePayment(sampleSingleRequest);
            verify(storageService).storeFile(anyString(), eq(supportingFile));
            verify(supportingFilesRepository).save(any(SupportingFiles.class));
            verify(submissionRepository, atLeast(2)).save(any(Submission.class)); // Service saves multiple times
            verify(approvalService).updateStatus(anyList(), eq("123456789"), eq("John Doe"));
        }
    }

    @Test
    @DisplayName("Should handle single submission when validation fails")
    void testSingleSubmission_ValidationFails() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service to return error
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(400)
                    .status("Error")
                    .message("Validation failed")
                    .build();
            when(validationService.validateSinglePayment(sampleSingleRequest))
                    .thenReturn(validationResponse);

            // When
            GeneralBodyResponse response = submissionService.singleSubmission(sampleSingleRequest, supportingFiles);

            // Then
            assertNotNull(response);
            assertEquals(400, response.getCode());
            assertEquals("Error", response.getStatus());
            assertEquals("Validation failed", response.getMessage());

            // Verify that no storage or repository operations were performed
            verify(storageService, never()).storeFile(anyString(), any(MultipartFile.class));
            verify(submissionRepository, never()).save(any(Submission.class));
        }
    }

    @Test
    @DisplayName("Should handle single submission without supporting files")
    void testSingleSubmission_NoSupportingFiles() {
        // Given
        List<MultipartFile> supportingFiles = null;

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Validation successful")
                    .data(samplePaymentData)
                    .build();
            when(validationService.validateSinglePayment(sampleSingleRequest))
                    .thenReturn(validationResponse);

            // Mock repositories
            when(submissionRepository.save(any(Submission.class))).thenReturn(sampleSubmission);
            when(submissionRepository.countByReferenceNumber(anyString())).thenReturn(0L);

            // When
            GeneralBodyResponse response = submissionService.singleSubmission(sampleSingleRequest, supportingFiles);

            // Then
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertEquals("OK", response.getStatus());
            assertTrue(response.getMessage().contains("Submission received successfully"));

            // Verify that no supporting file operations were performed
            verify(storageService, never()).storeFile(anyString(), any(MultipartFile.class));
            verify(supportingFilesRepository, never()).save(any(SupportingFiles.class));
            verify(submissionRepository, atLeast(1)).save(any(Submission.class)); // Service saves multiple times
        }
    }

    @Test
    @DisplayName("Should handle single submission when CustomBadRequestException is thrown")
    void testSingleSubmission_CustomBadRequestException() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service to throw CustomBadRequestException
            CustomBadRequestException customException = new CustomBadRequestException(400, "Bad Request", "Invalid data");
            when(validationService.validateSinglePayment(sampleSingleRequest))
                    .thenThrow(customException);

            // When
            GeneralBodyResponse response = submissionService.singleSubmission(sampleSingleRequest, supportingFiles);

            // Then
            assertNotNull(response);
            assertEquals(400, response.getCode());
            assertEquals("Bad Request", response.getStatus());
            assertEquals("Invalid data", response.getMessage());

            // Verify that no storage or repository operations were performed
            verify(storageService, never()).storeFile(anyString(), any(MultipartFile.class));
            verify(submissionRepository, never()).save(any(Submission.class));
        }
    }

    @Test
    @DisplayName("Should handle single submission when general exception is thrown")
    void testSingleSubmission_GeneralException() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service to throw RuntimeException
            when(validationService.validateSinglePayment(sampleSingleRequest))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // When
            GeneralBodyResponse response = submissionService.singleSubmission(sampleSingleRequest, supportingFiles);

            // Then
            assertNotNull(response);
            assertEquals(500, response.getCode());
            assertEquals("Error", response.getStatus());
            assertTrue(response.getMessage().contains("Error processing submission"));
            assertTrue(response.getMessage().contains("Database connection failed"));
            assertTrue(response.getMessage().contains(sampleSingleRequest.getNip()));

            // Verify that no storage or repository operations were performed
            verify(storageService, never()).storeFile(anyString(), any(MultipartFile.class));
            verify(submissionRepository, never()).save(any(Submission.class));
        }
    }

    @Test
    @DisplayName("Should handle bulk submission when supporting file storage fails")
    void testBulkSubmission_SupportingFileStorageFails() {
        // Given
        List<MultipartFile> supportingFiles = Arrays.asList(supportingFile);
        List<PaymentDataDTO> validatedData = Arrays.asList(samplePaymentData);

        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Mock security context
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            // Mock employee client
            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            // Mock validation service
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Validation successful")
                    .data(validatedData)
                    .build();
            when(validationService.validateExcelFile(mainFile))
                    .thenReturn(validationResponse);

            // Mock file operations
            when(mainFile.getOriginalFilename()).thenReturn("test.xlsx");
            when(mainFile.getContentType()).thenReturn("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            when(mainFile.getSize()).thenReturn(1024L);

            lenient().when(supportingFile.getOriginalFilename()).thenReturn("support.pdf");
            lenient().when(supportingFile.getContentType()).thenReturn("application/pdf");
            lenient().when(supportingFile.getSize()).thenReturn(512L);

            // Mock storage service - main file succeeds, supporting file fails
            FileDTO mainFileDTO = new FileDTO();
            mainFileDTO.setName("test.xlsx");
            when(storageService.storeFile(anyString(), eq(mainFile))).thenReturn(mainFileDTO);
            when(storageService.storeFile(anyString(), eq(supportingFile))).thenReturn(null);

            // Mock repositories
            when(mainFileRepository.save(any(MainFile.class))).thenReturn(sampleMainFile);
            when(submissionRepository.save(any(Submission.class))).thenReturn(sampleSubmission);
            when(submissionRepository.countByReferenceNumber(anyString())).thenReturn(0L);

            // When
            List<GeneralBodyResponse> responses = submissionService.bulkSubmission(mainFile, supportingFiles);

            // Then
            assertNotNull(responses);
            assertEquals(1, responses.size());
            assertEquals(200, responses.get(0).getCode()); // Should still succeed despite supporting file failure

            // Verify that supporting file repository save was not called
            verify(supportingFilesRepository, never()).save(any(SupportingFiles.class));
            // But main submission should still be saved
            verify(submissionRepository, atLeast(1)).save(any(Submission.class)); // Service saves multiple times
        }
    }

    @Test
    @DisplayName("Should generate unique reference numbers")
    void testGenerateReferenceNumber_Uniqueness() {
        // Given
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            securityUtilMock.when(SecurityUtil::getCurrentUserLogin)
                    .thenReturn(Optional.of("123456789"));
            securityUtilMock.when(SecurityUtil::getCurrentUserRole)
                    .thenReturn(Arrays.asList("ROLE_ADMIN_HRBP"));

            when(employeeClient.getProfile("123456789"))
                    .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

            List<PaymentDataDTO> validatedData = Arrays.asList(samplePaymentData);
            GeneralBodyResponse validationResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .data(validatedData)
                    .build();
            when(validationService.validateExcelFile(mainFile)).thenReturn(validationResponse);

            when(mainFile.getOriginalFilename()).thenReturn("test.xlsx");
            when(mainFile.getContentType()).thenReturn("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            when(mainFile.getSize()).thenReturn(1024L);

            FileDTO fileDTO = new FileDTO();
            when(storageService.storeFile(anyString(), eq(mainFile))).thenReturn(fileDTO);
            when(mainFileRepository.save(any(MainFile.class))).thenReturn(sampleMainFile);
            when(submissionRepository.save(any(Submission.class))).thenReturn(sampleSubmission);

            // Mock reference number collision on first attempt, then success
            when(submissionRepository.countByReferenceNumber(anyString()))
                    .thenReturn(1L) // First call returns collision
                    .thenReturn(0L); // Second call returns no collision

            // When
            List<GeneralBodyResponse> responses = submissionService.bulkSubmission(mainFile, Collections.emptyList());

            // Then
            assertNotNull(responses);
            assertEquals(1, responses.size());
            assertEquals(200, responses.get(0).getCode());

            // Verify that reference number uniqueness was checked multiple times
            verify(submissionRepository, atLeast(2)).countByReferenceNumber(anyString());
        }
    }
}
