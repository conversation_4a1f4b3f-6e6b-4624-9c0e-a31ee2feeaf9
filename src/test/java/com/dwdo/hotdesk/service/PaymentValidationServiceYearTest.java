package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentValidationServiceYearTest {

    @Mock
    private EmployeeClient employeeClient;

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @InjectMocks
    private PaymentValidationService paymentValidationService;

    private EpiccEmployeeProfileImpl mockEmployeeProfile;

    @BeforeEach
    void setUp() {
        mockEmployeeProfile = new EpiccEmployeeProfileImpl();
        mockEmployeeProfile.setDirectorate("IT");
        mockEmployeeProfile.setTerminationDate(null);
    }

    @Test
    void testValidYear2024() {
        // Given
        SingleValidRequestDTO request = createValidRequest("2024");
        when(employeeClient.getProfile(anyString())).thenReturn(ResponseEntity.ok(mockEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString())).thenReturn(false);

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        PaymentDataDTO data = (PaymentDataDTO) response.getData();
        assertEquals("2024", data.getYearOfProcess());
    }

    @Test
    void testValidYear2025() {
        // Given
        SingleValidRequestDTO request = createValidRequest("2025");
        when(employeeClient.getProfile(anyString())).thenReturn(ResponseEntity.ok(mockEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString())).thenReturn(false);

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        PaymentDataDTO data = (PaymentDataDTO) response.getData();
        assertEquals("2025", data.getYearOfProcess());
    }

    @Test
    void testValidYear2100() {
        // Given
        SingleValidRequestDTO request = createValidRequest("2100");
        when(employeeClient.getProfile(anyString())).thenReturn(ResponseEntity.ok(mockEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString())).thenReturn(false);

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        PaymentDataDTO data = (PaymentDataDTO) response.getData();
        assertEquals("2100", data.getYearOfProcess());
    }

    @Test
    void testInvalidYear2019() {
        // Given
        SingleValidRequestDTO request = createValidRequest("2019");

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(request);
        });

        assertTrue(exception.getMessage().contains("Year of Process must be between 2020 and 2100"));
    }

    @Test
    void testInvalidYear2101() {
        // Given
        SingleValidRequestDTO request = createValidRequest("2101");

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(request);
        });

        assertTrue(exception.getMessage().contains("Year of Process must be between 2020 and 2100"));
    }

    @Test
    void testInvalidYearFormat() {
        // Given
        SingleValidRequestDTO request = createValidRequest("abc");

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(request);
        });

        assertTrue(exception.getMessage().contains("Year of Process must be a valid 4-digit year"));
    }

    @Test
    void testYearExtractionFromText() {
        // Given
        SingleValidRequestDTO request = createValidRequest("Year 2024 Process");
        when(employeeClient.getProfile(anyString())).thenReturn(ResponseEntity.ok(mockEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString())).thenReturn(false);

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        PaymentDataDTO data = (PaymentDataDTO) response.getData();
        assertEquals("2024", data.getYearOfProcess());
    }

    private SingleValidRequestDTO createValidRequest(String year) {
        return SingleValidRequestDTO.builder()
                .nip("12345")
                .name("Test Employee")
                .grade("U5")
                .paymentType("Bonus Staggered")
                .amount(new BigDecimal("1000000"))
                .description("Test bonus")
                .monthOfProcess("January")
                .yearOfProcess(year)
                .build();
    }
}
