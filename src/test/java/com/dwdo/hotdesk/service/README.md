# Service Layer Unit Tests

This document describes the comprehensive unit tests for the service layer, covering core business logic including submission functionality and payment validation.

## Test Overview

The test suite consists of **5 focused test classes** with **78 total tests**:

1. **`SubmissionServiceTest`** (12 tests) - Tests for the core submission service functionality
2. **`PaymentValidationServiceTest`** (22 tests) - Tests for payment validation and Excel processing functionality
3. **`SubmissionHistoryServiceTest`** (11 tests) - Tests for submission history retrieval and approval details integration
4. **`TaskListServiceTest`** (8 tests) - Tests for approval task list retrieval and current user filtering
5. **`SubmissionFilterServiceTest`** (25 tests) - Tests for date parsing, filter specification building, and pagination functionality

These tests ensure that the services work correctly under various scenarios including success cases, error handling, authentication, authorization, and edge cases.

## Test Coverage

### SubmissionServiceTest (12 tests)

Tests for the core submission service functionality:

#### **Bulk Submission Tests (6 tests)**

1. **testBulkSubmission_Success** - Tests successful bulk submission with valid Excel file and supporting files
2. **testBulkSubmission_ValidationFails** - Tests handling when Excel validation fails
3. **testBulkSubmission_UserNotAuthenticated** - Tests authentication error handling
4. **testBulkSubmission_InvalidUserRole** - Tests authorization error for invalid user roles
5. **testBulkSubmission_MainFileStorageFails** - Tests handling when main file storage fails
6. **testBulkSubmission_SupportingFileStorageFails** - Tests handling when supporting file storage fails

#### **Single Submission Tests (5 tests)**

1. **testSingleSubmission_Success** - Tests successful single submission with supporting files
2. **testSingleSubmission_ValidationFails** - Tests handling when single payment validation fails
3. **testSingleSubmission_NoSupportingFiles** - Tests single submission without supporting files
4. **testSingleSubmission_CustomBadRequestException** - Tests custom exception handling
5. **testSingleSubmission_GeneralException** - Tests general exception handling

#### **Utility Tests (1 test)**

1. **testGenerateReferenceNumber_Uniqueness** - Tests reference number generation and collision handling

### PaymentValidationServiceTest (22 tests)

Tests for payment validation and Excel processing functionality:

#### **Single Payment Validation Tests (15 tests)**

1. **testValidateSinglePayment_Success** - Tests successful single payment validation with valid employee data
2. **testValidateSinglePayment_InvalidNip** - Tests handling when employee NIP is invalid
3. **testValidateSinglePayment_EmployeeClientException** - Tests handling when employee service throws exception
4. **testValidateSinglePayment_GeneralException** - Tests graceful handling of general exceptions
5. **testValidateSinglePayment_MissingNip** - Tests validation error when NIP is missing
6. **testValidateSinglePayment_MissingName** - Tests validation error when name is missing
7. **testValidateSinglePayment_MissingAmount** - Tests validation error when amount is missing
8. **testValidateSinglePayment_MissingGrade** - Tests validation error when grade is missing
9. **testValidateSinglePayment_MissingPaymentType** - Tests validation error when payment type is missing
10. **testValidateSinglePayment_MissingDescription** - Tests validation error when description is missing
11. **testValidateSinglePayment_MissingMonthOfProcess** - Tests validation error when month is missing
12. **testValidateSinglePayment_MissingYearOfProcess** - Tests validation error when year is missing
13. **testValidateSinglePayment_MonthNormalization** - Tests month value normalization (numeric to text)
14. **testValidateSinglePayment_MonthAbbreviation** - Tests month abbreviation normalization
15. **testValidateSinglePayment_WithTerminationDate** - Tests handling employee with termination date

#### **Excel File Validation Tests (5 tests)**

1. **testValidateExcelFile_InvalidFileType** - Tests rejection of non-Excel files
2. **testValidateExcelFile_InvalidContent** - Tests handling of corrupted Excel content
3. **testValidateExcelFile_ValidXlsFile** - Tests acceptance of XLS file type
4. **testValidateExcelFile_ValidXlsxFile** - Tests acceptance of XLSX file type
5. **testValidateExcelFile_NullContentType** - Tests rejection of files with null content type

#### **Edge Case Tests (2 tests)**

1. **testValidateSinglePayment_InvalidMonthNormalization** - Tests handling of invalid month values
2. **testValidateSinglePayment_EmployeeResponseNullBody** - Tests handling when employee response has null body

### SubmissionHistoryServiceTest (11 tests)

Tests for submission history retrieval and approval details integration:

#### **Get Submission History Tests (6 tests)**

1. **testGetSubmissionHistory_Success_DefaultParams** - Tests successful retrieval with default parameters
2. **testGetSubmissionHistory_Success_WithFilters** - Tests retrieval with filtering parameters (status, dates, NIP, name)
3. **testGetSubmissionHistory_Success_CurrentUserOnly** - Tests filtering by current user when currentUserOnly is true
4. **testGetSubmissionHistory_EmptyResults** - Tests handling of empty submission history results
5. **testGetSubmissionHistory_FilterServiceException** - Tests handling of filter service exceptions
6. **testGetSubmissionHistory_GeneralException** - Tests handling of general exceptions and wrapping in CustomBadRequestException

#### **Update Submission with Approval Details Tests (5 tests)**

1. **testUpdateSubmissionWithApprovalDetails_Success** - Tests successful update with approval details
2. **testUpdateSubmissionWithApprovalDetails_NullResponse** - Tests handling of null response from approval client
3. **testUpdateSubmissionWithApprovalDetails_UnsuccessfulResponse** - Tests handling of unsuccessful response from approval client
4. **testUpdateSubmissionWithApprovalDetails_NullData** - Tests handling of response with null data
5. **testUpdateSubmissionWithApprovalDetails_ClientException** - Tests handling of approval client exceptions

### TaskListServiceTest (8 tests)

Tests for approval task list retrieval and current user filtering:

#### **Get Approval Task List Tests (5 tests)**

1. **testGetApprovalTaskList_Success_DefaultParams** - Tests successful retrieval with default parameters
2. **testGetApprovalTaskList_Success_WithFilters** - Tests retrieval with filtering parameters (status, dates, NIP, name)
3. **testGetApprovalTaskList_Success_CurrentReviewerFilter** - Tests filtering by current reviewer (current user)
4. **testGetApprovalTaskList_EmptyResults** - Tests handling of empty task list results
5. **testGetApprovalTaskList_CurrentUserNotFound** - Tests exception when current user not found
6. **testGetApprovalTaskList_FilterServiceException** - Tests handling of filter service exceptions
7. **testGetApprovalTaskList_RepositoryException** - Tests handling of repository exceptions

#### **Mapping Tests (3 tests)**

1. **testMapToTaskListDTO_Success** - Tests correct mapping from Submission to TaskListDTO
2. **testMapToTaskListDTO_WithNullValues** - Tests mapping with null values in submission
3. **testMapToTaskListDTO_MultipleSubmissions** - Tests mapping with multiple submissions

### SubmissionFilterServiceTest (40 tests)

Tests for date parsing, date range validation, partial date filtering, filter specification building, and pagination functionality:

#### **Date Parsing Tests (10 tests)**

1. **testParseStartDate_ValidDateString** - Tests parsing valid date string to LocalDateTime (start of day)
2. **testParseEndDate_ValidDateString** - Tests parsing valid date string to LocalDateTime (end of day)
3. **testParseStartDate_ValidISODateTime** - Tests parsing ISO datetime string format
4. **testParseStartDate_NullInput** - Tests handling of null input (returns null)
5. **testParseStartDate_EmptyInput** - Tests handling of empty string input (returns null)
6. **testParseStartDate_InvalidFormat** - Tests exception throwing for invalid date format
7. **testParseEndDate_NullInput** - Tests handling of null end date input
8. **testParseEndDate_EmptyInput** - Tests handling of empty end date input
9. **testParseEndDate_InvalidFormat** - Tests exception throwing for invalid end date format
10. **testParseDates_DifferentFormats** - Tests handling of various valid date formats

#### **Date Range Validation Tests (10 tests)**

1. **testValidateDateRange_ValidRange** - Tests validation passes when start date is before end date
2. **testValidateDateRange_BothNull** - Tests validation passes when both dates are null
3. **testValidateDateRange_StartDateNull** - Tests validation passes when start date is null
4. **testValidateDateRange_EndDateNull** - Tests validation passes when end date is null
5. **testValidateDateRange_EqualDates** - Tests validation passes when dates are equal
6. **testValidateDateRange_StartAfterEnd** - Tests exception thrown when start date is after end date
7. **testValidateDateRange_OneDayDifference** - Tests exception thrown for one day difference (start after end)
8. **testValidateDateRange_OnlyStartDate** - Tests validation passes and logs partial filtering for start date only
9. **testValidateDateRange_OnlyEndDate** - Tests validation passes and logs partial filtering for end date only
10. **testValidateDateRange_BothNullWithLogging** - Tests validation passes and logs no filtering when both dates are null

#### **Fetch Submissions Tests (8 tests)**

1. **testFetchSubmissions_Success_AllFilters** - Tests successful fetching with all filter parameters
2. **testFetchSubmissions_Success_MinimalFilters** - Tests fetching with minimal/no filters
3. **testFetchSubmissions_EmptyResults** - Tests handling of empty result sets
4. **testFetchSubmissions_RepositoryException** - Tests handling of repository exceptions
5. **testFetchSubmissions_InvalidDateRange** - Tests exception thrown when start date is after end date
6. **testFetchSubmissions_ValidDateRange** - Tests successful fetching with valid date range
7. **testFetchSubmissions_OnlyStartDate** - Tests fetching submissions from start date onwards (partial filtering)
8. **testFetchSubmissions_OnlyEndDate** - Tests fetching submissions up to end date (partial filtering)

#### **Filter Specification Tests (7 tests)**

1. **testBuildFilterSpecification_AllFilters** - Tests building specification with all filter parameters
2. **testBuildFilterSpecification_NullFilters** - Tests building specification with null filters
3. **testBuildFilterSpecification_PartialFilters** - Tests building specification with partial filters
4. **testBuildFilterSpecification_InvalidDateRange** - Tests exception thrown when start date is after end date
5. **testBuildFilterSpecification_ValidDateRange** - Tests successful specification building with valid date range
6. **testBuildFilterSpecification_OnlyStartDate** - Tests building specification with only start date (partial filtering)
7. **testBuildFilterSpecification_OnlyEndDate** - Tests building specification with only end date (partial filtering)

#### **Pagination and Sorting Tests (8 tests)**

1. **testCreatePageable_DefaultSorting** - Tests creating pageable with default DESC sorting
2. **testCreatePageable_AscSorting** - Tests creating pageable with ASC sorting
3. **testCreatePageable_DescSortingDefault** - Tests default DESC sorting when not specified as ASC
4. **testCreateSortOrders_DefaultDirection** - Tests creating sort orders with default direction
5. **testCreateSortOrders_AscDirection** - Tests creating sort orders with ASC direction
6. **testCreateSortOrders_AscInMiddle** - Tests ASC detection when ASC is anywhere in sort array
7. **testCreateSortOrders_NoAscPresent** - Tests DESC direction when ASC not present
8. **testCreateSortOrders_EmptyArray** - Tests handling of empty sort array
9. **testCreateSortOrders_CaseInsensitiveAsc** - Tests case insensitive ASC detection

## Test Framework and Dependencies

- **JUnit 5** - Main testing framework
- **Mockito Inline** - Advanced mocking framework with static mocking support
- **Spring Test** - ReflectionTestUtils for dependency injection
- **Static Mocking** - MockedStatic for SecurityUtil mocking

## Key Testing Patterns

### Service Testing Strategy

- **Static Mocking**: Uses `MockedStatic<SecurityUtil>` for security context mocking
- **Reflection Injection**: Uses `ReflectionTestUtils` to inject `@Autowired` dependencies
- **Lenient Stubbing**: Uses `lenient()` for optional mock interactions
- **Complex Verification**: Uses `atLeast()` for flexible verification counts
- **Comprehensive Scenarios**: Tests authentication, authorization, file storage, and error handling

### Mocking Strategy

- **External Services**: All external services (validation, approval, storage) are mocked
- **Security Context**: Static SecurityUtil methods are mocked for authentication testing
- **File Operations**: MultipartFile objects are mocked for file upload testing
- **Repository Operations**: All repository interactions are mocked
- **Employee Client**: Feign client is mocked using reflection injection

### Error Handling Testing

- **Authentication Errors**: Tests user not authenticated scenarios
- **Authorization Errors**: Tests invalid user role scenarios
- **Validation Errors**: Tests service validation failures
- **Storage Errors**: Tests file storage failures
- **General Exceptions**: Tests unexpected exception handling

## Test Scenarios Covered

### Authentication & Authorization

- ✅ User authentication validation
- ✅ User role authorization checks
- ✅ Security context mocking

### File Operations

- ✅ Main file storage success/failure
- ✅ Supporting file storage success/failure
- ✅ File metadata handling

### Business Logic

- ✅ Payment validation integration
- ✅ Reference number generation and uniqueness
- ✅ Submission creation and persistence
- ✅ Approval workflow integration

### Error Scenarios

- ✅ Service validation failures
- ✅ Storage service failures
- ✅ Custom exception handling
- ✅ General exception propagation

## Command Line Execution

```bash
# Run individual service test classes
mvn surefire:test -Dtest=SubmissionServiceTest
mvn surefire:test -Dtest=PaymentValidationServiceTest
mvn surefire:test -Dtest=SubmissionHistoryServiceTest
mvn surefire:test -Dtest=TaskListServiceTest
mvn surefire:test -Dtest=SubmissionFilterServiceTest

# Run specific test methods
mvn surefire:test -Dtest=SubmissionServiceTest#testBulkSubmission_Success
mvn surefire:test -Dtest=PaymentValidationServiceTest#testValidateSinglePayment_Success
mvn surefire:test -Dtest=SubmissionHistoryServiceTest#testGetSubmissionHistory_Success_DefaultParams
mvn surefire:test -Dtest=TaskListServiceTest#testGetApprovalTaskList_Success_DefaultParams
mvn surefire:test -Dtest=SubmissionFilterServiceTest#testParseStartDate_ValidDateString

# Run all service tests
mvn surefire:test -Dtest=*ServiceTest

# Run all tests with skipTests disabled (temporary override)
mvn test -DskipTests=false
```

## Test Results

All 78 tests pass successfully, providing comprehensive coverage of the service layer functionality:

**SubmissionService Tests:**

```
[INFO] Tests run: 12, Failures: 0, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS
```

**PaymentValidationService Tests:**

```
[INFO] Tests run: 22, Failures: 0, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS
```

**SubmissionHistoryService Tests:**

```
[INFO] Tests run: 11, Failures: 0, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS
```

**TaskListService Tests:**

```
[INFO] Tests run: 8, Failures: 0, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS
```

**SubmissionFilterService Tests:**

```
[INFO] Tests run: 25, Failures: 0, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS
```

## Benefits

- **Complete Service Coverage**: Tests submission workflows and payment validation functionality
- **Security Testing**: Validates authentication and authorization flows
- **Error Resilience**: Ensures proper error handling and user feedback
- **Integration Confidence**: Validates interactions with external services (employee client, storage)
- **File Processing**: Comprehensive Excel file validation and processing tests
- **Data Validation**: Thorough testing of payment data validation rules
- **Month Normalization**: Tests for proper month value conversion and handling
- **Maintainability**: Well-structured tests that are easy to understand and maintain
