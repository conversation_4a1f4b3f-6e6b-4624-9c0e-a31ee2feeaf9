package com.dwdo.hotdesk.service;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class FileTemplateServiceTest {

    @InjectMocks
    private FileTemplateService fileTemplateService;

    @Test
    void testGenerateStaggeredPaymentTemplate_YearDisplaysCorrectly() throws IOException {
        // When
        ResponseEntity<Resource> response = fileTemplateService.generateStaggeredPaymentTemplate();

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());

        // Verify the Excel content
        try (InputStream inputStream = response.getBody().getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            assertNotNull(sheet);
            assertEquals("Submission Template", sheet.getSheetName());

            // Check header row
            Row headerRow = sheet.getRow(0);
            assertNotNull(headerRow);
            assertEquals("Year of Process", headerRow.getCell(7).getStringCellValue());

            // Check first data row - should show 2025, not 1905
            Row firstDataRow = sheet.getRow(1);
            assertNotNull(firstDataRow);
            
            Cell yearCell = firstDataRow.getCell(7);
            assertNotNull(yearCell);
            
            // The year should be stored as text "2025", not as a formatted date
            String yearValue = yearCell.getStringCellValue();
            assertEquals("2025", yearValue);
            
            // Verify other sample data rows also have correct years
            for (int i = 1; i <= 5; i++) {
                Row dataRow = sheet.getRow(i);
                if (dataRow != null) {
                    Cell dataYearCell = dataRow.getCell(7);
                    if (dataYearCell != null) {
                        String dataYearValue = dataYearCell.getStringCellValue();
                        assertEquals("2025", dataYearValue, "Row " + i + " should have year 2025");
                    }
                }
            }
        }
    }

    @Test
    void testGenerateStaggeredPaymentTemplate_HasCorrectHeaders() throws IOException {
        // When
        ResponseEntity<Resource> response = fileTemplateService.generateStaggeredPaymentTemplate();

        // Then
        try (InputStream inputStream = response.getBody().getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            
            String[] expectedHeaders = {
                "NIP", "Name", "Grade", "Payment Type", "Amount (IDR)", 
                "Description", "Month of Process", "Year of Process"
            };
            
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell headerCell = headerRow.getCell(i);
                assertNotNull(headerCell, "Header cell " + i + " should not be null");
                assertEquals(expectedHeaders[i], headerCell.getStringCellValue(), 
                    "Header " + i + " should match expected value");
            }
        }
    }

    @Test
    void testGenerateStaggeredPaymentTemplate_HasSampleData() throws IOException {
        // When
        ResponseEntity<Resource> response = fileTemplateService.generateStaggeredPaymentTemplate();

        // Then
        try (InputStream inputStream = response.getBody().getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            // Should have header row + 5 sample data rows
            assertTrue(sheet.getLastRowNum() >= 5, "Should have at least 5 data rows plus header");
            
            // Check first sample row
            Row firstRow = sheet.getRow(1);
            assertNotNull(firstRow);
            assertEquals("1234567", firstRow.getCell(0).getStringCellValue()); // NIP
            assertEquals("Adi Surya", firstRow.getCell(1).getStringCellValue()); // Name
            assertEquals("U2", firstRow.getCell(2).getStringCellValue()); // Grade
            assertEquals("Bonus Staggered", firstRow.getCell(3).getStringCellValue()); // Payment Type
            assertEquals(15000000.0, firstRow.getCell(4).getNumericCellValue()); // Amount
            assertEquals("February", firstRow.getCell(6).getStringCellValue()); // Month
            assertEquals("2025", firstRow.getCell(7).getStringCellValue()); // Year
        }
    }
}
