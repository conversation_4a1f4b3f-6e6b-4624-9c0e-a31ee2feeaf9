package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.request.GeneratePaymentRequestDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.Resource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("GeneratePaymentService Tests")
class GeneratePaymentServiceTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @InjectMocks
    private GeneratePaymentService generatePaymentService;

    private GeneratePaymentRequestDTO sampleRequest;
    private Submission sampleSubmission;

    @BeforeEach
    void setUp() {
        sampleRequest = GeneratePaymentRequestDTO.builder()
                .yearOfProcess("2024")
                .monthOfProcess("January")
                .build();

        sampleSubmission = Submission.builder()
                .id(1L)
                .referenceNumber("REF-001")
                .submitterName("John Doe")
                .submitterJob("HRBP")
                .status("Waiting for Payment")
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary Adjustment")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .directorate("IT")
                .eligible(true)
                .build();
    }

    @Nested
    @DisplayName("Generate Payment Excel Tests")
    class GeneratePaymentExcelTests {

        @Test
        @DisplayName("Should successfully generate Excel for submissions with Waiting for Payment status")
        void testGeneratePaymentExcel_Success() {
            // Given
            List<Submission> mockSubmissions = Arrays.asList(sampleSubmission);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(mockSubmissions);

            // When
            ResponseEntity<Resource> result = generatePaymentService.generatePaymentExcel(sampleRequest);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getStatusCode().is2xxSuccessful()).isTrue();
            assertThat(result.getBody()).isNotNull();
            assertThat(result.getHeaders().getContentDisposition().getFilename()).contains("payment_export_");

            verify(submissionRepository).findAll(any(Specification.class));
        }

        @Test
        @DisplayName("Should filter by status, year, and month of process")
        void testGeneratePaymentExcel_FilteringCriteria() {
            // Given
            List<Submission> mockSubmissions = Arrays.asList(sampleSubmission);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(mockSubmissions);

            // When
            generatePaymentService.generatePaymentExcel(sampleRequest);

            // Then
            verify(submissionRepository).findAll(any(Specification.class));
            // The specification should filter by:
            // - status = "Waiting for Payment"
            // - yearOfProcess = "2024"
            // - monthOfProcess = "January"
        }

        @Test
        @DisplayName("Should throw exception when no submissions found")
        void testGeneratePaymentExcel_NoSubmissionsFound() {
            // Given
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(Collections.emptyList());

            // When & Then
            assertThatThrownBy(() -> generatePaymentService.generatePaymentExcel(sampleRequest))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("No submissions found with status 'Waiting for Payment' for January 2024");

            verify(submissionRepository).findAll(any(Specification.class));
        }

        @Test
        @DisplayName("Should throw exception for invalid year")
        void testGeneratePaymentExcel_InvalidYear() {
            // Given
            GeneratePaymentRequestDTO invalidRequest = GeneratePaymentRequestDTO.builder()
                    .yearOfProcess("invalid")
                    .monthOfProcess("January")
                    .build();

            // When & Then
            assertThatThrownBy(() -> generatePaymentService.generatePaymentExcel(invalidRequest))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Year of process must be a valid number");
        }

        @Test
        @DisplayName("Should throw exception for invalid month")
        void testGeneratePaymentExcel_InvalidMonth() {
            // Given
            GeneratePaymentRequestDTO invalidRequest = GeneratePaymentRequestDTO.builder()
                    .yearOfProcess("2024")
                    .monthOfProcess("InvalidMonth")
                    .build();

            // When & Then
            assertThatThrownBy(() -> generatePaymentService.generatePaymentExcel(invalidRequest))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Invalid month of process");
        }

        @Test
        @DisplayName("Should handle different year and month combinations")
        void testGeneratePaymentExcel_DifferentYearMonth() {
            // Given
            GeneratePaymentRequestDTO differentRequest = GeneratePaymentRequestDTO.builder()
                    .yearOfProcess("2023")
                    .monthOfProcess("December")
                    .build();

            List<Submission> mockSubmissions = Arrays.asList(sampleSubmission);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(mockSubmissions);

            // When
            ResponseEntity<Resource> result = generatePaymentService.generatePaymentExcel(differentRequest);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getStatusCode().is2xxSuccessful()).isTrue();

            verify(submissionRepository).findAll(any(Specification.class));
        }

        @Test
        @DisplayName("Should generate Excel with correct headers")
        void testGeneratePaymentExcel_CorrectHeaders() {
            // Given
            List<Submission> mockSubmissions = Arrays.asList(sampleSubmission);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(mockSubmissions);

            // When
            ResponseEntity<Resource> result = generatePaymentService.generatePaymentExcel(sampleRequest);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getHeaders().getContentType().toString())
                    .contains("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            assertThat(result.getHeaders().getContentDisposition().getType()).isEqualTo("attachment");

            verify(submissionRepository).findAll(any(Specification.class));
        }

        @Test
        @DisplayName("Should handle bonus payment types correctly")
        void testGeneratePaymentExcel_BonusPaymentTypes() {
            // Given
            Submission bonusSubmission = Submission.builder()
                    .id(2L)
                    .referenceNumber("REF-002")
                    .submitterName("Jane Smith")
                    .submitterJob("HRREWARD")
                    .status("Waiting for Payment")
                    .nip("987654321")
                    .name("Jane Smith")
                    .grade("B")
                    .paymentType("Bonus Staggered")
                    .amount(new BigDecimal("2000000"))
                    .description("Performance bonus")
                    .monthOfProcess("January")
                    .yearOfProcess("2024")
                    .directorate("HR")
                    .eligible(true)
                    .build();

            List<Submission> mockSubmissions = Arrays.asList(sampleSubmission, bonusSubmission);
            when(submissionRepository.findAll(any(Specification.class))).thenReturn(mockSubmissions);

            // When
            ResponseEntity<Resource> result = generatePaymentService.generatePaymentExcel(sampleRequest);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getStatusCode().is2xxSuccessful()).isTrue();

            verify(submissionRepository).findAll(any(Specification.class));
        }
    }
}
