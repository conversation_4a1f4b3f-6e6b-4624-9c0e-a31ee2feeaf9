package com.dwdo.hotdesk.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataDTO {
    private Long id;
    private String referenceNumber;
    private String submitterName;
    private String submitterJob;
    private String status;
    private String nip;
    private String name;
    private String grade;
    private String paymentType;
    private BigDecimal amount;
    private String description;
    private String monthOfProcess;
    private String yearOfProcess;
    private String directorate;
    private String slik;
    private String sanction;
    private String terminationDate;
    private Boolean eligible;
    private String paymentDate;
    private String remarks;
    private String reportDate;
    private String generatedBy;
}
