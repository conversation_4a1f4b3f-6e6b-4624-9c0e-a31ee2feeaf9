package com.dwdo.hotdesk.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SingleValidRequestDTO {
    @NotBlank(message = "NIP is required")
    private String nip;
    
    private String name;

    private String grade;
    
    @NotBlank(message = "Payment Type is required")
    private String paymentType;
    
    @NotNull(message = "Amount is required")
    private BigDecimal amount;
    
    @NotBlank(message = "Description is required")
    private String description;
    
    @NotBlank(message = "Month of Process is required")
    private String monthOfProcess;
    
    @NotBlank(message = "Year of Process is required")
    private String yearOfProcess;
}
