package com.dwdo.hotdesk.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeneratePaymentRequestDTO {
    @NotBlank(message = "Year of process is required")
    private String yearOfProcess;

    @NotBlank(message = "Month of process is required")
    private String monthOfProcess;
}
