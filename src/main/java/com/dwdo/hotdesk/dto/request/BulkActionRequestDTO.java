package com.dwdo.hotdesk.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkActionRequestDTO {

    @NotEmpty(message = "Task names are required")
    private List<String> taskNames;

    @NotBlank(message = "Action is required")
    private String action;

    private String reason;

    // Optional fields for action processing
    private String monthOfProcess;
    private String yearOfProcess;
    private String paymentType;
    private BigDecimal amount;
}
