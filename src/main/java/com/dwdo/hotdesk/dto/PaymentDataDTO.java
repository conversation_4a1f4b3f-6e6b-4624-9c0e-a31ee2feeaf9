package com.dwdo.hotdesk.dto;

import lombok.*;
import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class PaymentDataDTO {
    private String nip;
    private String nipValid;
    private String name;
    private String grade;
    private String gradeValid;
    private String paymentType;
    private String paymentValid;
    private BigDecimal amount;
    private String description;
    private String monthOfProcess;
    private String yearOfProcess;
    private String directorate;
    private String slik;
    private String sanction;
    private String terminationDate;
    private Boolean eligible;
    private String isDuplicate;

}
