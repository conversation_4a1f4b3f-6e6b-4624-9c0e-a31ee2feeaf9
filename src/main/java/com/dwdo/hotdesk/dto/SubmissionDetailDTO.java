package com.dwdo.hotdesk.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmissionDetailDTO {
    private Long id;
    private String taskName;
    private LocalDateTime createdAt;
    private String referenceNumber;
    private String createdBy;
    private String submitterName;
    private String submitterJob;
    private String status;
    private String nip;
    private String name;
    private String grade;
    private String paymentType;
    private BigDecimal amount;
    private String description;
    private String monthOfProcess;
    private String yearOfProcess;
    private String directorate;
    private String slik;
    private String sanction;
    private String terminationDate;
    private Boolean eligible;
    private String currentReviewer;
    private String paymentDate;
    private String remarks;
    private List<SupportingFileDTO> supportingFiles;
}
