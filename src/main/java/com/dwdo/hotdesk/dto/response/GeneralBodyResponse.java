package com.dwdo.hotdesk.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GeneralBodyResponse implements Serializable {
    private int code;
    private String status;
    private String message;
    
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object data;
}
