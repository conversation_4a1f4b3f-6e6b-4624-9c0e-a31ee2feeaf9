package com.dwdo.hotdesk.dto.response;

import org.springframework.beans.factory.annotation.Value;

public interface EpiccEmployeeProfileDTO {
    String getId();

    String getFirstName();

    String getMiddleName();

    String getLastName();

    String getEmployeeNumber();

    String getKnownAs();

    @Value(value = "#{target.active_status == 1}")
    Boolean getActive();

    String getOriginalDateOfHire();

    String getHireDate();

    String getSex();

    String getDateOfBirth();

    String getTownOfBirth();

    String getMaritalStatus();

    String getNationality();

    String getReligion();

    String getOfficialEmail();

    String getPosition();

    String getDirectorate();

    String getSubDirectorate();

    String getRcCode();

    byte[] getImgProfile();

    String getPositionTitle();
    
    String getPeopleGroupName();
    
    String getDivision();
    
    String getStatus();

    String getDepartmentName();
    
    String getJobCode();
    
    Double getAge();
    
    String getGrade();
    
    String getGradeCode();

    String getOfficeBranch();
    
    String getKtpNumber();
    
    String getNpwpNumber();
    
    String getBpjsNumber();
    
    String getTerminationDate();
    
    String getPrivateEmail();
    
    String getOfficePhone();
    
    String getPhoneNumber();
    
    String getAddress();
    
    Long getLocationId();
    
    Long getPaymentId();
    
    String getMotherMaidenName();
    
    String getCountryOfBirth();

    String getReportingLine();
    
    @Value(value = "#{target.is_nha > 0}")
    Boolean getIsNha();

    String getGroup();

    String getResignationStatus();

    String getPermanentDate();

    String getGenericJobCode();
    
    String getGenericJobName();
    
    String getJobLevel();
    
    String getCorporateTitle();
    
    String getActualTerminationDate();
}
