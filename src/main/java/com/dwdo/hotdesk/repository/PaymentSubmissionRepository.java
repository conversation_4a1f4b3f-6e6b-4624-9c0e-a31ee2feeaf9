package com.dwdo.hotdesk.repository;

import com.dwdo.hotdesk.model.Submission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentSubmissionRepository extends JpaRepository<Submission, Long>, JpaSpecificationExecutor<Submission> {
    long countByReferenceNumber(String referenceNumber);

    Optional<Submission> findByReferenceNumber(String referenceNumber);

    Optional<Submission> findByTaskName(String taskName);

    List<Submission> findByTaskNameIn(List<String> taskNames);

    @Query("SELECT s FROM Submission s LEFT JOIN FETCH s.supportingFiles WHERE s.id = :id")
    Optional<Submission> findByIdWithSupportingFiles(@Param("id") Long id);

    @Query("SELECT COUNT(s) > 0 FROM Submission s WHERE s.nip = :nip AND s.monthOfProcess = :monthOfProcess AND s.yearOfProcess = :yearOfProcess AND s.paymentType = :paymentType")
    boolean existsByCompositeKey(@Param("nip") String nip,
                                @Param("monthOfProcess") String monthOfProcess,
                                @Param("yearOfProcess") String yearOfProcess,
                                @Param("paymentType") String paymentType);
}
