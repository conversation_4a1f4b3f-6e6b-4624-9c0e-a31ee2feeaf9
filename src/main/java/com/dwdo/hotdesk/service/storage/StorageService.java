package com.dwdo.hotdesk.service.storage;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface StorageService {
	String uploadDocument(String directoryCode, String subdirectoryCode, String docTypeCode, String nip, MultipartFile file);
	
	String uploadDocument(String directoryCode, String subdirectoryCode, String docTypeCode, String nip, MultipartFile file, String newFileName);
	
	List<String> listDocuments(String directoryCode, String subdirectoryCode, String docTypeCode, String nip);
	
	byte[] downloadDocumentFile(String documentCode);
	
	void  downloadDocumentFile(String documentCode, String outputDir) throws IOException;
	
	void deleteDocumentFile(String documentCode);
}
