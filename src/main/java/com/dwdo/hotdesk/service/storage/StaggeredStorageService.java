package com.dwdo.hotdesk.service.storage;

import com.dwdo.hotdesk.config.StorageConfig;
import com.dwdo.hotdesk.dto.FileDTO;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service(StaggeredStorageService.SERVICE_NAME)
public class StaggeredStorageService implements CloudStorageService{

    public static final String SERVICE_NAME = "staggeredStorageService";
    @Autowired
    @Qualifier(StorageConfig.GCS_BEAN)
    private MinioClient minioClient;
    @Value("${storage.gcs.endpoint}")
    private String endpoint;
    @Value("${storage.gcs.bucket}")
    private String bucket;
    @Value("${storage.gcs.url.expiry}")
    private Integer preSignedUrlExpiry;


    @Override
    public FileDTO loadFile(String filePath) {
        FileDTO response = new FileDTO();
        try {
            try (GetObjectResponse object = minioClient.getObject(GetObjectArgs.builder()
                    .object(filePath)
                    .bucket(bucket)
                    .build())) {
                response.setContentType(object.headers().get(HttpHeaders.CONTENT_TYPE));
                response.setName(object.object());
                response.setUrl(this.getPreSignedUrl(object.object()));
                response.setFile(IOUtils.toByteArray(object));
            }
            return response;
        } catch (ErrorResponseException e) {
            logError("Error loading file (ErrorResponseException) at {}: {}", filePath, e);
        } catch (IllegalArgumentException e) {
            logError("Illegal argument error loading file at {}: {}", filePath, e);
        } catch (InsufficientDataException e) {
            logError("Insufficient data error loading file at {}: {}", filePath, e);
        } catch (InternalException e) {
            logError("Internal error loading file at {}: {}", filePath, e);
        } catch (InvalidKeyException e) {
            logError("Invalid key error loading file at {}: {}", filePath, e);
        } catch (InvalidResponseException e) {
            logError("Invalid response error loading file at {}: {}", filePath, e);
        } catch (NoSuchAlgorithmException e) {
            logError("No such algorithm error loading file at {}: {}", filePath, e);
        } catch (ServerException e) {
            logError("Server error loading file at {}: {}", filePath, e);
        } catch (XmlParserException e) {
            logError("XML parser error loading file at {}: {}", filePath, e);
        } catch (IOException e) {
            logError("I/O error loading file at {}: {}", filePath, e);
        } catch (SecurityException e) {
            logError("Security error during loading file", filePath, e);
        } catch (Exception e) {
            logError("Unexpected error loading file at {}: {}", filePath, e);
        }
    
        throw new CustomBadRequestException(400, "Bad Request", "Error loading file");
    }


    @Override
    public void deleteFile(String filePath) {
		log.info("Start delete file {} from google cloud storage", filePath);
        boolean isSuccess = false;
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucket)
                    .object(filePath)
                    .build());
            log.info("Finish delete file : {}",filePath);
            isSuccess = true;
        } catch (ErrorResponseException e) {
            logError("Error during file deletion (ErrorResponseException)", e);
        } catch (IllegalArgumentException e) {
            logError("Error during file deletion (IllegalArgumentException)", e);
        } catch (InsufficientDataException e) {
            logError("Error during file deletion (InsufficientDataException)", e);
        } catch (InternalException e) {
            logError("Internal error during file deletion", e);
        } catch (InvalidKeyException e) {
            logError("Invalid key error during file deletion", e);
        } catch (InvalidResponseException e) {
            logError("Invalid response error during file deletion", e);
        } catch (NoSuchAlgorithmException e) {
            logError("No such algorithm error during file deletion", e);
        } catch (ServerException e) {
            logError("Server error during file deletion", e);
        } catch (XmlParserException e) {
            logError("XML parser error during file deletion", e);
        } catch (IOException e) {
            logError("I/O error during file deletion", e);
        } catch (SecurityException e) {
            logError("Security error during file deletion", e);
        } catch (Exception e) {
            logError("Unexpected error during file deletion", e);
        }
        
        if (!isSuccess)
            throw new CustomBadRequestException(400, "Bad Request", "Error deleting file");
    }

    @Override
    public FileDTO storeFile(String object, MultipartFile file) {
        log.info("Start upload file {} to google cloud storage, path : {}", file.getOriginalFilename(), object);
        FileDTO response = new FileDTO();
        try {
            byte[] bytes = file.getBytes();
            try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
                ObjectWriteResponse objectWriteResponse = minioClient.putObject(PutObjectArgs.builder()
                        .stream(inputStream, bytes.length, -1)
                        .contentType(file.getContentType())
                        .bucket(bucket)
                        .object(object)
                        .build());
                response.setName(objectWriteResponse.object());
                response.setUrl(this.getPreSignedUrl(objectWriteResponse.object()));
                response.setContentType(file.getContentType());
            }
            log.info("Finish upload file : {}", response.getName());
            return response;
        } catch (ErrorResponseException e) {
            logError("Error during file upload", e);
        } catch (IllegalArgumentException e) {
            logError("Illegal argument during file upload", e);
        } catch (InsufficientDataException e) {
            logError("Insufficient data during file upload", e);
        } catch (InternalException e) {
            logError("Internal error during file upload", e);
        } catch (InvalidKeyException e) {
            logError("Invalid key during file upload", e);
        } catch (InvalidResponseException e) {
            logError("Invalid response during file upload", e);
        } catch (NoSuchAlgorithmException e) {
            logError("No such algorithm during file upload", e);
        } catch (ServerException e) {
            logError("Server error during file upload", e);
        } catch (XmlParserException e) {
            logError("XML parser error during file upload", e);
        } catch (IOException e) {
            logError("I/O error during file upload", e);
        } catch (SecurityException e) {
            logError("Security error during file upload", e);
        } catch (Exception e) {
            logError("Unexpected error during file upload", e);
        }
    
        throw new CustomBadRequestException(400, "Bad Request", "Error uploading file");
    }

    @Override
    public String getPreSignedUrl(String filePath) {
        log.info("Start getPreSignedUrl : {}", filePath);
        try {
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .bucket(bucket)
                    .object(filePath)
                    .expiry(preSignedUrlExpiry, TimeUnit.SECONDS)
                    .method(Method.GET)
                    .build());
        } catch (ErrorResponseException e) {
            logError("Error generating presigned URL (ErrorResponseException)", filePath, e);
        } catch (IllegalArgumentException e) {
            logError("Error generating presigned URL (IllegalArgumentException)", filePath, e);
        } catch (InsufficientDataException e) {
            logError("Insufficient data error during generating presigned URL", filePath, e);
        } catch (InternalException e) {
            logError("Internal error during generating presigned URL", filePath, e);
        } catch (InvalidKeyException e) {
            logError("Invalid key error during generating presigned URL", filePath, e);
        } catch (InvalidResponseException e) {
            logError("Invalid response during generating presigned URL", e);
        } catch (NoSuchAlgorithmException e) {
            logError("No such algorithm during generating presigned URL", e);
        } catch (ServerException e) {
            logError("Server error during generating presigned URL", e);
        } catch (XmlParserException e) {
            logError("XML parser error during generating presigned URL", e);
        } catch (IOException e) {
            logError("I/O error during generating presigned URL", filePath, e);
        } catch (SecurityException e) {
            logError("Security error during generating presigned URL", filePath, e);
        } catch (Exception e) {
            logError("Unexpected error during generating presigned URL", filePath, e);
        }
        return null;
    }

    @Override
    public void clear() {
        //do nothing
    }

    @Override
    public String getPreSignedUrl(String filePath, Integer duration, TimeUnit timeUnit) {
        log.info("Start getPreSignedUrl : {} with duration {} and timeUnit {}", filePath, duration, timeUnit);
        try {
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .bucket(bucket)
                    .object(filePath)
                    .expiry(duration, timeUnit)
                    .method(Method.GET)
                    .build());
        } catch (ErrorResponseException e) {
            logError("Error generating presigned URL (ErrorResponseException)", filePath, e);
        } catch (IllegalArgumentException e) {
            logError("Error generating presigned URL (IllegalArgumentException)", filePath, e);
        } catch (InsufficientDataException e) {
            logError("Insufficient data error during generating presigned URL", filePath, e);
        } catch (InternalException e) {
            logError("Internal error during generating presigned URL", filePath, e);
        } catch (InvalidKeyException e) {
            logError("Invalid key error during generating presigned URL", filePath, e);
        } catch (InvalidResponseException e) {
            logError("Invalid response during generating presigned URL", e);
        } catch (NoSuchAlgorithmException e) {
            logError("No such algorithm during generating presigned URL", e);
        } catch (ServerException e) {
            logError("Server error during generating presigned URL", e);
        } catch (XmlParserException e) {
            logError("XML parser error during generating presigned URL", e);
        } catch (IOException e) {
            logError("I/O error during generating presigned URL", filePath, e);
        } catch (SecurityException e) {
            logError("Security error during generating presigned URL", filePath, e);
        } catch (Exception e) {
            logError("Unexpected error during generating presigned URL", filePath, e);
        }
        return null;
    }
        
    private void logError(String message, String filePath, Exception e) {
        log.error("{}: filePath={}, message={}", message, filePath, e.getMessage());
    }

    private void logError(String message, Exception e) {
        log.error("{}: message={}", message, e.getMessage());
    }
}
