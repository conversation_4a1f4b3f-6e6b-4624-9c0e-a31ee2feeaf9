package com.dwdo.hotdesk.service.storage;

import com.dwdo.hotdesk.util.TemplateUtil;
import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("mockStorageService")
@Profile("dev") // Ensure this service is used in the development profile
public class MockStorageService implements StorageService {

    private final Map<String, byte[]> storage = new HashMap<>();

    @Override
    public String uploadDocument(String directoryCode, String subdirectoryCode, String docTypeCode, String nip, MultipartFile file) {
    	return uploadDocument(directoryCode, subdirectoryCode, docTypeCode, nip, file, file.getOriginalFilename());
    }
    
    @Override
    public String uploadDocument(String directoryCode, String subdirectoryCode, String docTypeCode, String nip, MultipartFile file, String newFileName) {
        try {
            // Simulate file upload by storing the file in the in-memory map
        	String fileCode=TemplateUtil.buildPath(directoryCode, subdirectoryCode, docTypeCode, nip, newFileName);
            storage.put(fileCode, file.getBytes());
            return fileCode;
        } catch (IOException e) {
            throw new RuntimeException("Error uploading file", e);
        }
    }
    
    @Override
    public List<String> listDocuments(String directoryCode, String subdirectoryCode, String docTypeCode, String nip) {
        // Return a list of all file names stored in the mock
        return new ArrayList<>(storage.keySet());
    }
    
    @Override
    public byte[] downloadDocumentFile(String documentCode) {
        // Simulate file download by retrieving the file from the in-memory map
        byte[] fileData = storage.get(documentCode);
        if (fileData == null) {
            throw new RuntimeException("File not found: " + documentCode);
        }
        return fileData;
    }

	@Override
	public void downloadDocumentFile(String documentCode, String outputDir) throws IOException {
		byte[] myByteArray=downloadDocumentFile(documentCode);
		FileUtils.writeByteArrayToFile(new File(outputDir+File.separator+documentCode), myByteArray);
	}

	@Override
	public void deleteDocumentFile(String documentCode) {
		storage.remove(documentCode);
	}
    
}

