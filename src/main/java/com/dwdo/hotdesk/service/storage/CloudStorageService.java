package com.dwdo.hotdesk.service.storage;

import com.dwdo.hotdesk.dto.FileDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.TimeUnit;

public interface CloudStorageService {


    FileDTO loadFile(String filePath);

    void deleteFile(String filePath);

    FileDTO storeFile(String folder, MultipartFile file);

    String getPreSignedUrl(String filePath);

    void clear();

    String getPreSignedUrl(String filePath, Integer duration, TimeUnit timeUnit);

}
