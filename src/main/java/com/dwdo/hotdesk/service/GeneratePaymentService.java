package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.request.GeneratePaymentRequestDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeneratePaymentService {

    private final PaymentSubmissionRepository submissionRepository;

    private static final String STATUS_WAITING_FOR_PAYMENT = "Waiting for Payment";

    private static final Set<String> BONUS_PAYMENT_TYPES = new HashSet<>(Arrays.asList(
        "Bonus Staggered", "Retention Bonus", "Token", "Performance Staggered"
    ));

    private static final String ELEMENT_NAME_BONUS = "Bonus Gross";
    private static final String ELEMENT_NAME_MERIT = "Merit Increase(MI)";

    public ResponseEntity<Resource> generatePaymentExcel(GeneratePaymentRequestDTO request) {
        log.info("[GeneratePaymentService] Starting Excel generation for submissions with status '{}', year: {}, month: {}",
                STATUS_WAITING_FOR_PAYMENT, request.getYearOfProcess(), request.getMonthOfProcess());

        try {
            validateRequest(request);
            Specification<Submission> spec = Specification.where(null);

            spec = spec.and((root, query, cb) -> cb.equal(root.get("status"), STATUS_WAITING_FOR_PAYMENT));

            spec = spec.and((root, query, cb) -> cb.equal(root.get("yearOfProcess"), request.getYearOfProcess()));

            spec = spec.and((root, query, cb) -> cb.equal(root.get("monthOfProcess"), request.getMonthOfProcess()));

            List<Submission> submissions = submissionRepository.findAll(spec);
            log.info("[GeneratePaymentService] Retrieved {} submissions matching criteria", submissions.size());

            if (submissions.isEmpty()) {
                String message = String.format("No submissions found with status '%s' for %s %s",
                        STATUS_WAITING_FOR_PAYMENT, request.getMonthOfProcess(), request.getYearOfProcess());
                log.warn("[GeneratePaymentService] {}", message);
                throw new CustomBadRequestException(404, "Not Found", message);
            }

            byte[] excelData = createExcelFile(submissions);
            
            ByteArrayResource resource = new ByteArrayResource(excelData);
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = String.format("payment_export_%s.xlsx", timestamp);
            
            log.info("[GeneratePaymentService] Excel file generated successfully with {} records", submissions.size());
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .contentLength(resource.contentLength())
                    .body(resource);

        } catch (CustomBadRequestException e) {
            log.error("[GeneratePaymentService] Validation error: {}", e.getMessage());
            throw e;
        } catch (IOException e) {
            log.error("[GeneratePaymentService] IO error while creating Excel file", e);
            throw new CustomBadRequestException(500, "Internal Server Error",
                    "Failed to create Excel file due to IO error: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("[GeneratePaymentService] Invalid argument error: {}", e.getMessage());
            throw new CustomBadRequestException(400, "Bad Request",
                    "Invalid request parameters: " + e.getMessage());
        } catch (Exception e) {
            log.error("[GeneratePaymentService] Unexpected error generating Excel file", e);
            throw new CustomBadRequestException(500, "Internal Server Error",
                    "An unexpected error occurred while generating Excel file: " + e.getMessage());
        }
    }

    private byte[] createExcelFile(List<Submission> submissions) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Export");

            CellStyle dateStyle = workbook.createCellStyle();
            CreationHelper createHelper = workbook.getCreationHelper();
            dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-MMM-yyyy"));

            CellStyle generalStyle = workbook.createCellStyle();
            generalStyle.setDataFormat(createHelper.createDataFormat().getFormat("General"));

            createHeaderRow(workbook, sheet);

            createDataRows(sheet, submissions, dateStyle, generalStyle);

            for (int i = 0; i < 6; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    private void createHeaderRow(Workbook workbook, Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        String[] headers = {"BATCH_NAME", "EFFECTIVE_DATE", "ELEMENT_NAME", "ASSIGNMENT_NUMBER", "INPUT_VALUE1", "INPUT_VALUE2"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private void createDataRows(Sheet sheet, List<Submission> submissions, CellStyle dateStyle, CellStyle generalStyle) {
        int rowNum = 1;

        for (Submission submission : submissions) {
            Row row = sheet.createRow(rowNum++);

            row.createCell(0).setCellValue("");

            Date effectiveDate = parseEffectiveDate(submission.getMonthOfProcess(), submission.getYearOfProcess());
            Cell dateCell = row.createCell(1);
            dateCell.setCellValue(effectiveDate);
            dateCell.setCellStyle(dateStyle);

            String elementName = getElementName(submission.getPaymentType());
            row.createCell(2).setCellValue(elementName);

            row.createCell(3).setCellValue("E" + submission.getNip());

            Cell amountCell = row.createCell(4);
            amountCell.setCellValue(submission.getAmount().doubleValue());
            amountCell.setCellStyle(generalStyle);

            row.createCell(5).setCellValue(submission.getReferenceNumber());
        }
    }

    private Date parseEffectiveDate(String monthOfProcess, String yearOfProcess) {
        try {
            Calendar calendar = Calendar.getInstance();

            calendar.set(Calendar.YEAR, Integer.parseInt(yearOfProcess));
            calendar.set(Calendar.MONTH, getMonthNumber(monthOfProcess));
            calendar.set(Calendar.DAY_OF_MONTH, 1);

            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            return calendar.getTime();
        } catch (Exception e) {
            log.warn("[GeneratePaymentService] Error parsing date for month: {}, year: {}. Using current date.",
                    monthOfProcess, yearOfProcess);

            Calendar fallbackCalendar = Calendar.getInstance();
            fallbackCalendar.set(Calendar.HOUR_OF_DAY, 0);
            fallbackCalendar.set(Calendar.MINUTE, 0);
            fallbackCalendar.set(Calendar.SECOND, 0);
            fallbackCalendar.set(Calendar.MILLISECOND, 0);
            return fallbackCalendar.getTime();
        }
    }

    private int getMonthNumber(String monthName) {
        if (monthName == null || monthName.trim().isEmpty()) {
            return 0;
        }

        String month = monthName.trim().toLowerCase();
        return switch (month) {
            case "january" -> 0;
            case "february" -> 1;
            case "march" -> 2;
            case "april" -> 3;
            case "may" -> 4;
            case "june" -> 5;
            case "july" -> 6;
            case "august" -> 7;
            case "september" -> 8;
            case "october" -> 9;
            case "november" -> 10;
            case "december" -> 11;
            default -> 0;
        };
    }

    private String getElementName(String paymentType) {
        if (paymentType == null) {
            return ELEMENT_NAME_MERIT;
        }
        String trimmedPaymentType = paymentType.trim();
        return BONUS_PAYMENT_TYPES.contains(trimmedPaymentType) ? ELEMENT_NAME_BONUS : ELEMENT_NAME_MERIT;
    }

    private void validateRequest(GeneratePaymentRequestDTO request) {
        if (request == null) {
            throw new CustomBadRequestException(400, "Bad Request", "Request cannot be null");
        }

        String yearOfProcess = request.getYearOfProcess();
        if (yearOfProcess == null || yearOfProcess.trim().isEmpty()) {
            throw new CustomBadRequestException(400, "Bad Request", "Year of process is required");
        }

        String monthOfProcess = request.getMonthOfProcess();
        if (monthOfProcess == null || monthOfProcess.trim().isEmpty()) {
            throw new CustomBadRequestException(400, "Bad Request", "Month of process is required");
        }

        try {
            int year = Integer.parseInt(yearOfProcess.trim());
            if (year < 1900 || year > 2100) {
                throw new CustomBadRequestException(400, "Bad Request",
                        "Year of process must be between 1900 and 2100, got: " + year);
            }
        } catch (NumberFormatException e) {
            throw new CustomBadRequestException(400, "Bad Request",
                    "Year of process must be a valid number, got: " + yearOfProcess);
        }

        String month = monthOfProcess.trim();
        if (getMonthNumber(month) == 0 && !month.equalsIgnoreCase("january")) {
            throw new CustomBadRequestException(400, "Bad Request",
                    "Invalid month of process. Expected month names like 'January', 'February', etc., got: " + month);
        }
    }
}
