package com.dwdo.hotdesk.service.feign.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeLevelResponse implements Serializable {
    private String level;
    private Employee employee;
    private EmployeeName employeeName;
    private EmployeeEmail employeeEmail;
    private EmployeeEmail employeeOfficeEmail;
    private PeriodsOfService periodsOfService;
    private List<Education> educations;
    private Assignment assignment;
    private List<NationalIdentifier> nationalIdentifiers;
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Employee implements Serializable {
        private Long personId;
        private String personNumber;
        private String effectiveStartDate;
        private String effectiveEndDate;
        private Long businessGroupId;
        private Long personTypeId;
        private String primaryFlag;
        private String systemPersonType;
        private String workTermsNumber;
        private Long legalEntityId;
        private String legislationCode;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeName implements Serializable {
        private Long personNameId;
        private Long personId;
        private String effectiveStartDate;
        private String effectiveEndDate;
        private String legislationCode;
        private String namingType;
        private String firstName;
        private String middleNames;
        private String lastName;
        private String title;
        private String displayName;
        private String listName;
        private String fullName;
        private String orderName;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeEmail implements Serializable {
        private Long emailAddressId;
        private Long personId;
        private String emailType;
        private String emailAddress;
        private String primaryFlag;
        private String dateFrom;
        private String dateTo;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PeriodsOfService implements Serializable {
        private Long periodOfServiceId;
        private Long personId;
        private String periodType;
        private String actualTerminationDate;
        private Long legalEntityId;
        private String workerNumber;
        private String primaryFlag;
        private String originalDateOfHire;
        private String dateStart;
        private String dateEnd;
        private String workerType;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Education implements Serializable {
        private Long talentProfileItemId;
        private Long personId;
        private Long contentItemTypeId;
        private Long contentTypeId;
        private String dateFrom;
        private String dateTo;
        private String schoolOrInstitution;
        private String schoolType;
        private String educationLevel;
        private String educationMajor;
        private String educationMinor;
        private String gpa;
        private String gpaScale;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Assignment implements Serializable {
        private Long assignmentId;
        private String assignmentNumber;
        private Long assignmentStatusTypeId;
        private String assignmentType;
        private Long businessUnitId;
        private String effectiveStartDate;
        private String effectiveEndDate;
        private String employeeNumber;
        private String employeeCategory;
        private Long jobId;
        private String jobCode;
        private String jobName;
        private Long locationId;
        private String locationCode;
        private String locationName;
        private String position;
        private String positionCode;
        private String positionName;
        private Long departmentId;
        private String departmentName;
        private Long gradeId;
        private String gradeName;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NationalIdentifier implements Serializable {
        private Long personNationalIdentifierId;
        private Long personId;
        private String nationalIdentifierType;
        private String nationalIdentifierNumber;
        private String issuingCountry;
        private String issuingLocation;
        private String issueDate;
        private String expirationDate;
        private String primaryFlag;
    }
    
    public String getFullName() {
        if (employeeName == null) {
            return "";
        }
        
        StringBuilder fullName = new StringBuilder();
        if (employeeName.getFirstName() != null) {
            fullName.append(employeeName.getFirstName());
        }
        
        if (employeeName.getMiddleNames() != null && !employeeName.getMiddleNames().isEmpty()) {
            if (fullName.length() > 0) {
                fullName.append(" ");
            }
            fullName.append(employeeName.getMiddleNames());
        }
        
        if (employeeName.getLastName() != null && !employeeName.getLastName().isEmpty()) {
            if (fullName.length() > 0) {
                fullName.append(" ");
            }
            fullName.append(employeeName.getLastName());
        }
        
        return fullName.toString();
    }
    
    public String getJobName() {
        return assignment != null ? assignment.getJobName() : "";
    }
    
    public String getDirectorate() {
        return assignment != null ? assignment.getDepartmentName() : "";
    }
}
