package com.dwdo.hotdesk.service.feign;

import com.dwdo.hotdesk.service.feign.request.SubmitTransactionRequest;
import com.dwdo.hotdesk.service.feign.request.TransactionActionRequest;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "approval", url = "${feign.approval}")
public interface ApprovalClient {
	@PostMapping(value = "/api/microservice/transaction-console/submit",
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<ApiResponse> submitTransaction(@RequestBody SubmitTransactionRequest request);

	@GetMapping(value = "/api/microservice/transaction-console/detail", produces = MediaType.APPLICATION_JSON_VALUE)
	ApiResponse detail(@RequestParam String taskName);

	@PostMapping(value = "/api/microservice/transaction-console/action")
	ResponseEntity<?> action(@RequestBody TransactionActionRequest request);

}
