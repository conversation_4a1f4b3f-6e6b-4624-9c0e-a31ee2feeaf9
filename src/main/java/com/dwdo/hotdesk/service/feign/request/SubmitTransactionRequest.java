package com.dwdo.hotdesk.service.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubmitTransactionRequest implements Serializable {

	private String ruleCode;
	private String systemCode;
	private Map<String, Object> parameter;
	private String createdBy;
	private String createdByName;
	private List<List<String>> layers;
	private String processName;
	private String dataDetail;
	private String processOwner;
	private String processOwnerName;

	private String anchorWeb;
	private String anchorMobile;
}