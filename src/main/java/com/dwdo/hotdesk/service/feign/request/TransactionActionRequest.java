package com.dwdo.hotdesk.service.feign.request;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TransactionActionRequest {

	// private Long layerId;
	private String taskName;

	private String approverCode;

	// while approver code is not nik
	private String approverUsername;
	private String approverName;

	private String action;
	private String approvalDetail;
	private String reason;
	private String description;
	private String note;

	private String nextReviewer;
	private String backApprovalCode;
	private String nextApprovalCode;

	private Boolean isAdmin;

	private String username;

	// for add approver / pic
	// approver Code is current pic
	private List<String> approvers;
	private Map<String, Object> additionalData;

	private LocalDateTime updatedAt;

	List<MultipartFile> files;

}
