package com.dwdo.hotdesk.service.feign.response;

import com.dwdo.hotdesk.dto.response.EpiccEmployeeProfileDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EpiccEmployeeProfileImpl implements EpiccEmployeeProfileDTO {
    private String id;
    private String firstName;
    private String middleName;
    private String lastName;
    private String employeeNumber;
    private String knownAs;
    private Boolean active;
    private String originalDateOfHire;
    private String hireDate;
    private String sex;
    private String dateOfBirth;
    private String townOfBirth;
    private String maritalStatus;
    private String nationality;
    private String religion;
    private String officialEmail;
    private String position;
    private String directorate;
    private String subDirectorate;
    private String rcCode;
    private byte[] imgProfile;
    private String positionTitle;
    private String peopleGroupName;
    private String division;
    private String status;
    private String departmentName;
    private String jobCode;
    private Double age;
    private String grade;
    private String gradeCode;
    private String officeBranch;
    private String ktpNumber;
    private String npwpNumber;
    private String bpjsNumber;
    private String terminationDate;
    private String privateEmail;
    private String officePhone;
    private String phoneNumber;
    private String address;
    private Long locationId;
    private Long paymentId;
    private String motherMaidenName;
    private String countryOfBirth;
    private String reportingLine;
    private Boolean isNha;
    private String group;
    private String resignationStatus;
    private String permanentDate;
    private String genericJobCode;
    private String genericJobName;
    private String jobLevel;
    private String corporateTitle;
    private String actualTerminationDate;
}
