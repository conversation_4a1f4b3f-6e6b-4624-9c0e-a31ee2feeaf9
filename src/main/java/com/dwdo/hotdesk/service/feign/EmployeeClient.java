package com.dwdo.hotdesk.service.feign;

import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "employee", url = "${feign.employee}")
public interface EmployeeClient {
    @GetMapping("/api/microservice/authentication/get-profile")
    ResponseEntity<EpiccEmployeeProfileImpl> getProfile(@RequestParam("nip") String nip);
}
