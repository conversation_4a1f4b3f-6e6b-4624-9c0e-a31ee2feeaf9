package com.dwdo.hotdesk.service.feign.response;

import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DetailResponse {

	private Long id;
	private LocalDateTime createdAt;
	private LocalDateTime updatedAt;
	private String createdBy;
	private String updatedBy;
	private Map<String, Object> currentLayerState;
	private Object data;
	private LocalDateTime expiryDate;
	private String taskName;
	private List<Map<String, Object>> layers;
	private String status;
	private String statusCode;
	private String sendBackTo;
	private List<Map<String, Object>> workflows;
	private String createdByName;
	private String processName;
	private Map<String, Object> submitter;
	private Map<String, Object> processOwner;
	private String dataDetail;
	private String actions;
	private Boolean isUsingAnchor;
	private String anchorWeb;
	private String anchorMobile;
}
