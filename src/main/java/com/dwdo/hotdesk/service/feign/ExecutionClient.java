package com.dwdo.hotdesk.service.feign;

import com.dwdo.hotdesk.service.feign.request.ExecutionRequest;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "execution", url = "${feign.execution}")
public interface ExecutionClient {
    @PostMapping(value = "/api/microservice/execute",
            produces = MediaType.APPLICATION_JSON_VALUE,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    ApiResponse executeLogic(@RequestBody ExecutionRequest request);
}
