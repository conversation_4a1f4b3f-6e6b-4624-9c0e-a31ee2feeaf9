package com.dwdo.hotdesk.util;

import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TemplateUtil {
	
	public static String buildPath(String directoryCode, String subdirectoryCode, String docTypeCode, String nip, String filename) {
		
//		filename=filename.substring(filename.lastIndexOf("/"),filename.length()-1);
		return Stream.of(directoryCode, subdirectoryCode, docTypeCode, nip, filename) // Stream the parameters
				.filter(s -> s != null && !s.isEmpty()) // Exclude null or empty strings
				.collect(Collectors.joining("/")); // Join using "/" as a separator
	}
	
	public static String getUpdatedFileName(String originalFileName, String newFileName) {
        if (newFileName == null) {
            return originalFileName;
        }
        
        return newFileName;
        
//        String extension = "";
//        int lastIndexOfDot = originalFileName.lastIndexOf('.');
//        if (lastIndexOfDot != -1) {
//            extension = originalFileName.substring(lastIndexOfDot); // includes the dot
//        }
//        
//        return newFileName + extension;
    }
}
