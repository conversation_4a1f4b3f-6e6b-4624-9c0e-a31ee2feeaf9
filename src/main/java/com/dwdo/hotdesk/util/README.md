# NullUtil - Null Value Display Utility

## Overview

The `NullUtil` class provides consistent handling of null and empty values across the staggered payment service application. It ensures that null or empty database values are displayed as "-" in the user interface, providing a better user experience.

## Features

### 1. String Value Conversion
- Converts `null` strings to "-"
- Converts empty strings (`""`) to "-"
- Converts whitespace-only strings (`"   "`) to "-"
- Preserves non-empty strings as-is

### 2. LocalDate Value Conversion
- Converts `null` LocalDate to "-"
- Converts valid LocalDate to ISO string format (e.g., "2024-01-15")

### 3. Generic Object Conversion
- Converts `null` objects to "-"
- Converts non-null objects to their string representation

## Usage Examples

### Basic String Conversion
```java
// Null handling
String result1 = NullUtil.toDisplayString((String) null);  // Returns "-"
String result2 = NullUtil.toDisplayString("");             // Returns "-"
String result3 = NullUtil.toDisplayString("   ");          // Returns "-"
String result4 = NullUtil.toDisplayString("Test");         // Returns "Test"
```

### LocalDate Conversion
```java
// Date handling
String result1 = NullUtil.toDisplayString((LocalDate) null);           // Returns "-"
String result2 = NullUtil.toDisplayString(LocalDate.of(2024, 1, 15));  // Returns "2024-01-15"
```

### In Service Mapping Methods
```java
// Before (manual null checking)
.paymentDate(submission.getPaymentDate() != null 
        ? submission.getPaymentDate().toString() : "-")
.remarks(submission.getRemarks() != null && !submission.getRemarks().trim().isEmpty() 
        ? submission.getRemarks() : "-")

// After (using NullUtil)
.paymentDate(NullUtil.toDisplayString(submission.getPaymentDate()))
.remarks(NullUtil.toDisplayString(submission.getRemarks()))
```

## Implementation

The utility is used in the following service classes:

1. **SubmissionDetailService** - `mapToDTO()` method
2. **SubmissionHistoryService** - `mapToDTO()` method  
3. **TaskListService** - `mapToTaskListDTO()` method
4. **ApprovalService** - `mapToTaskListDTO()` method

## Benefits

1. **Consistency**: All null values are displayed uniformly as "-"
2. **Maintainability**: Centralized null handling logic
3. **Readability**: Cleaner, more readable mapping code
4. **Type Safety**: Proper handling of different data types
5. **User Experience**: No null values displayed in the UI

## Testing

Comprehensive tests are available in `NullUtilTest.java` covering:
- Null string handling
- Empty/whitespace string handling
- LocalDate conversion
- Object conversion
- Edge cases

## Constants

- **NULL_DISPLAY_VALUE**: `"-"` - The value displayed for null/empty data

## Methods

| Method | Description |
|--------|-------------|
| `toDisplayString(String)` | Converts string to display format |
| `toDisplayString(LocalDate)` | Converts LocalDate to display format |
| `toDisplayString(Object)` | Converts object to display format |
| `isNullOrEmpty(String)` | Checks if string is null or empty |
| `getNullDisplayValue()` | Returns the null display constant |
