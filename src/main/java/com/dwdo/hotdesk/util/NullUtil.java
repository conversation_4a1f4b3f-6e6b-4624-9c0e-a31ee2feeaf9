package com.dwdo.hotdesk.util;

import java.time.LocalDate;


public class NullUtil {

    private static final String NULL_DISPLAY_VALUE = "-";

    private NullUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }


    public static String toDisplayString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return NULL_DISPLAY_VALUE;
        }
        return value;
    }

    public static String toDisplayString(LocalDate date) {
        if (date == null) {
            return NULL_DISPLAY_VALUE;
        }
        return date.toString();
    }

    public static String toDisplayString(Object value) {
        if (value == null) {
            return NULL_DISPLAY_VALUE;
        }
        return value.toString();
    }


    public static boolean isNullOrEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }


    public static String getNullDisplayValue() {
        return NULL_DISPLAY_VALUE;
    }
}
