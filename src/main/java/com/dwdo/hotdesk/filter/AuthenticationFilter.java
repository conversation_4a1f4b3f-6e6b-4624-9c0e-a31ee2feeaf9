package com.dwdo.hotdesk.filter;

import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.security.TokenProvider;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Slf4j
@AllArgsConstructor
public class AuthenticationFilter extends GenericFilterBean {
	private static final String AUTHORIZATION_HEADER = "Authorization";
	private final TokenProvider tokenProvider;
	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
		HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
		String path = httpServletRequest.getServletPath();
		if (!path.startsWith("/api/microservice")) {
			String token = resolveToken(httpServletRequest);
			if (StringUtils.hasText(token) && this.tokenProvider.validateToken(token)) {
				try {
					Authentication authentication = this.tokenProvider.getAuthentication(token);
					SecurityContextHolder.getContext().setAuthentication(authentication);
					log.info("{} : accessing : {}", SecurityUtil.getCurrentUserLogin().orElse(null), path);
				} catch (Exception e) {
					log.info("AuthenticationFilter : {}", e.getMessage());
				}
			}
		}
		filterChain.doFilter(servletRequest, servletResponse);
	}
	private String resolveToken(HttpServletRequest httpServletRequest) {
		String token = httpServletRequest.getHeader(AUTHORIZATION_HEADER);
		if (StringUtils.hasText(token) && token.startsWith("Bearer ")) {
			return token.substring(7);
		}
		return null;
	}
}
