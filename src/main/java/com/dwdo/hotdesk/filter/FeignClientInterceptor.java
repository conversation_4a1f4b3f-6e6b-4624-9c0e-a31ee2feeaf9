package com.dwdo.hotdesk.filter;

import com.dwdo.hotdesk.security.SecurityUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

@Component
public class FeignClientInterceptor implements RequestInterceptor {

	private static final String AUTHORIZATION_HEADER = "Authorization";
	private static final String BEARER = "Bearer";
	@Override
	public void apply(RequestTemplate requestTemplate) {

		SecurityUtil.getCurrentUserJWT()
				.ifPresent(s -> requestTemplate.header(AUTHORIZATION_HEADER,String.format("%s %s", BEARER, s)));
	}
}
