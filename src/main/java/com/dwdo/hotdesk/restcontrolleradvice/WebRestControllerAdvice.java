package com.dwdo.hotdesk.restcontrolleradvice;

import com.dwdo.hotdesk.dto.response.GeneralExceptionResponse;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class WebRestControllerAdvice extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        ex.printStackTrace();
        return new ResponseEntity<>("Failed", HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(CustomBadRequestException.class)
    public ResponseEntity<GeneralExceptionResponse> handleBadRequest(CustomBadRequestException exception) {
        GeneralExceptionResponse generalExceptionResponse = new GeneralExceptionResponse(exception.getCode(), exception.getStatus(), exception.getMessage());
        return ResponseEntity
            .status(exception.getCode())
            .body(generalExceptionResponse);
    }

    @ExceptionHandler(CustomNotFoundException.class)
    public ResponseEntity<GeneralExceptionResponse> handleNotFound(CustomNotFoundException exception) {
        GeneralExceptionResponse generalExceptionResponse = new GeneralExceptionResponse(exception.getCode(), exception.getStatus(), exception.getMessage());
        return ResponseEntity
            .status(exception.getCode())
            .body(generalExceptionResponse);
    }

    // Error handle for @Valid
    @Override
    @SuppressWarnings("NullableProblems")
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
    		MethodArgumentNotValidException ex, HttpHeaders headers,
    		HttpStatus status, WebRequest request) {
    	
        Map<String, Object> body = new LinkedHashMap<>();
        body.put("code", 400);
        body.put("status", "Bad Request");
        body.put("message", "Request body is invalid!");

        // Get all errors
        Map<String, String> errors = ex.getBindingResult().getFieldErrors().stream().collect(Collectors.toMap(FieldError::getField, FieldError::getDefaultMessage));
        body.put("errors", errors);

        return new ResponseEntity<>(body, headers, status);

    }
}
