package com.dwdo.hotdesk.restcontrolleradvice;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@Getter
@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class CustomBadRequestException extends RuntimeException {
    private final int code;
    private final String status;
    private final String message;

    public CustomBadRequestException(int code, String status, String message) {
        super(message);

        this.code = code;
        this.status = status;
        this.message = message;
    }

	public static CustomBadRequestException badRequest(String message) {
		return new CustomBadRequestException(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase(), message);
	}
}