package com.dwdo.hotdesk.model;

import com.dwdo.hotdesk.model.audit.DateAudit;
import lombok.*;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "main_file")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MainFile extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToMany(mappedBy = "mainFile")
    private List<Submission> submissions = new ArrayList<>();

    @Column(name = "file_name", nullable = false)
    private String fileName;

    @Column(name = "file_path", nullable = false)
    private String filePath;

    @Column(name = "file_type", nullable = false)
    private String fileType;

    @Column(name = "file_size", nullable = false)
    private Long fileSize;
}
