package com.dwdo.hotdesk.model.audit;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.dwdo.hotdesk.security.SecurityUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@JsonIgnoreProperties(value = { "createdAt", "updatedAt","createdBy","updatedBy" }, allowGetters = true)
@Setter
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public abstract class DateAudit implements Serializable {
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(nullable = true)
    private LocalDateTime updatedAt;

    @CreatedBy
    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @LastModifiedBy
    @Column(nullable = true)
    private String updatedBy;

    @PrePersist
    protected void onPrePersist()  {
        if (createdAt == null) {
            setCreatedAt(LocalDateTime.now(ZoneOffset.UTC));
        }
        if (createdBy == null) {
            SecurityUtil.getCurrentUserLogin().ifPresent(this::setCreatedBy);
        }
    }

    @PreUpdate
    protected void onPreUpdate() {
        setUpdatedAt(LocalDateTime.now(ZoneOffset.UTC));
        SecurityUtil.getCurrentUserLogin().ifPresent(this::setUpdatedBy);
    }
}
