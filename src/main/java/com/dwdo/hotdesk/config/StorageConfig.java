package com.dwdo.hotdesk.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StorageConfig {
    public static final String GCS_BEAN = "googleCloudStorage";
    @Value("${storage.gcs.endpoint}")
    private String gcsEndpoint;
    @Value("${storage.gcs.key.access}")
    private String gcsAccessKey;
    @Value("${storage.gcs.key.secret}")
    private String gcsSecretKey;

    @Bean(name = StorageConfig.GCS_BEAN)
    public MinioClient gcsMinioClient() {
        return MinioClient.builder()
                .endpoint(gcsEndpoint)
                .credentials(gcsAccessKey, gcsSecretKey)
                .build();
    }
}
