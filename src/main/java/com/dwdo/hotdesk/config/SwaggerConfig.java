package com.dwdo.hotdesk.config;

import com.fasterxml.classmate.TypeResolver;
import org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.data.domain.Pageable;
import springfox.documentation.builders.AlternateTypeBuilder;
import springfox.documentation.builders.AlternateTypePropertyBuilder;
import springfox.documentation.schema.AlternateTypeRule;
import springfox.documentation.schema.AlternateTypeRuleConvention;

import java.lang.reflect.Type;
import java.util.List;

import static springfox.documentation.schema.AlternateTypeRules.newRule;

@Configuration
public class SwaggerConfig {

    @Bean
    public TypeResolver TypeResolver() {
        return new TypeResolver();
    }
    @Bean
    public AlternateTypeRuleConvention pageableConvention(
            final TypeResolver resolver,
            final SpringDataWebProperties properties) {
        return new AlternateTypeRuleConvention() {

            @Override
            public int getOrder() {
                return Ordered.HIGHEST_PRECEDENCE;
            }

            @Override
            public List<AlternateTypeRule> rules() {
                return List.of(
                        newRule(resolver.resolve(Pageable.class), resolver.resolve(pageableMixin(properties)))
                );
            }
        };
    }

    private Type pageableMixin(final SpringDataWebProperties properties) {
        return new AlternateTypeBuilder()
                .fullyQualifiedClassName(
                        String.format("%s.generated.%s",
                                Pageable.class.getPackage().getName(),
                                Pageable.class.getSimpleName()))
                .property(b -> property(b, Integer.class, properties.getPageable().getPageParameter()))
                .property(b -> property(b, Integer.class, properties.getPageable().getSizeParameter()))
                .property(b -> property(b, String.class, properties.getSort().getSortParameter()))
                .build();
    }

    private void property(AlternateTypePropertyBuilder builder, Class<?> type, String name) {
        builder
                .name(name)
                .type(type)
                .canRead(true)
                .canWrite(true);
    }
}
