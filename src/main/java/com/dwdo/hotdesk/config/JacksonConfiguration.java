package com.dwdo.hotdesk.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.fasterxml.jackson.datatype.hibernate5.Hibernate5Module;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.support.PageJacksonModule;
import org.springframework.cloud.openfeign.support.SortJacksonModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

@Configuration
public class JacksonConfiguration {

	/**
	 * Support for Java date and time API.
	 *
	 * @return the corresponding Jackson module.
	 */
	@Bean
	public JavaTimeModule javaTimeModule() {
		return new JavaTimeModule();
	}

	@Bean
	public Jdk8Module jdk8TimeModule() {
		return new Jdk8Module();
	}

	/*
	 * Support for Hibernate types in Jackson.
	 */
	@Bean
	public Hibernate5Module hibernate5Module() {
		return new Hibernate5Module();
	}

	@Bean
	public ObjectMapper objectMapper(@Autowired JavaTimeModule javaTimeModule,
									 @Autowired Jdk8Module jdk8TimeModule,
									 @Autowired Hibernate5Module hibernate5Module) {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.setDateFormat(new StdDateFormat());
		objectMapper.registerModule(javaTimeModule);
		objectMapper.registerModule(jdk8TimeModule);
		objectMapper.registerModule(hibernate5Module);
		objectMapper.registerModule(new PageJacksonModule());
		objectMapper.registerModule(new SortJacksonModule());
		objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
		return objectMapper;
	}

	@Bean
	public MappingJackson2HttpMessageConverter jacksonMessageConverter(@Autowired JavaTimeModule javaTimeModule,
																	   @Autowired Jdk8Module jdk8TimeModule,
																	   @Autowired Hibernate5Module hibernate5Module) {
		return new MappingJackson2HttpMessageConverter(objectMapper(javaTimeModule, jdk8TimeModule, hibernate5Module));
	}
}