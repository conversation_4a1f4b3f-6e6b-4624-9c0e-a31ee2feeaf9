package com.dwdo.hotdesk.config;

import com.dwdo.hotdesk.security.JWTConfigurer;
import com.dwdo.hotdesk.security.SecurityProblemSupport;
import com.dwdo.hotdesk.security.TokenProvider;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;

@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
@AllArgsConstructor
@Import(SecurityProblemSupport.class)
@Configuration
public class SecurityConfiguration {
	private final TokenProvider tokenProvider;
	private final SecurityProblemSupport securityProblemSupport;

	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
		http
				.csrf()
				.disable()
				.exceptionHandling()
				.authenticationEntryPoint(securityProblemSupport)
				.accessDeniedHandler(securityProblemSupport)
			.and()
				.headers()
				.contentSecurityPolicy("default-src 'self'; frame-src 'self' data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://storage.googleapis.com; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:")
			.and()
				.referrerPolicy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)
			.and()
				.frameOptions()
				.disable()
			.and()
				.sessionManagement()
				.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
			.and()
				.authorizeRequests()
				.antMatchers("/api/microservice/**").permitAll()
				.antMatchers("/api/**").permitAll()
			.and()
				.apply(securityAdapter());
		return http.build();
	}

	private JWTConfigurer securityAdapter() {
		return new JWTConfigurer(tokenProvider);
	}
}
