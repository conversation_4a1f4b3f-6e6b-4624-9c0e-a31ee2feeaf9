package com.dwdo.hotdesk.config;

import org.modelmapper.Conditions;
import org.modelmapper.ModelMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class ModelMapperConfig {

	/*
	 * copy values with no condition
	 */
	@Bean
    @Primary
    public ModelMapper modelMapper() {
        return new ModelMapper();
    }

	/*
	 * copy values but ignore when the source is NULL. Use this when copying fields when update model object from DTO
	 */
    @Bean("modelMapperForUpdate")
    public ModelMapper modelMapperForUpdate() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setPropertyCondition(Conditions.isNotNull());
        return modelMapper;
    }
}
