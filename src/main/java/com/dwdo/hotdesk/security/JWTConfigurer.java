package com.dwdo.hotdesk.security;

import com.dwdo.hotdesk.filter.AuthenticationFilter;
import lombok.AllArgsConstructor;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@AllArgsConstructor
public class JWTConfigurer extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {
	private final TokenProvider tokenProvider;

	@Override
	public void configure(HttpSecurity builder) {
		AuthenticationFilter authenticationFilter = new AuthenticationFilter(tokenProvider);
		builder.addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class);
	}
}
