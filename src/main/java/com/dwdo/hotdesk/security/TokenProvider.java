package com.dwdo.hotdesk.security;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.UnsupportedJwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TokenProvider {
	private static final String AUTHORITIES_KEY = "auth";
	@Value("${jhipster.security.authentication.jwt.base64-secret}")
	private String jwtSecret;
	@Value("${jhipster.security.cryptography.key}")
	private String secretKey;

	public boolean validateToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(authToken);
            return true;
        } catch (ExpiredJwtException e) {
            log.info("Expired JWT token.");
            log.trace("Expired JWT token trace: {}", e.getMessage());
            return true;
        } catch (UnsupportedJwtException e) {
            log.info("Unsupported JWT token.");
            log.trace("Unsupported JWT token trace: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.info("JWT token compact of handler are invalid.");
            log.trace("JWT token compact of handler are invalid trace: {}", e.getMessage());
        }
        return false;
    }

	public String decrypt(String strToDecrypt) throws Exception {
        if (strToDecrypt == null || strToDecrypt.isEmpty()) return null;
        byte[] iv = new byte[96];
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);
        byte[] aadData = "symService".getBytes();
        SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(secretKey), "AES");
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec, new SecureRandom());
        cipher.updateAAD(aadData);
        return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)));
    }

	public Authentication getAuthentication(String token) throws Exception {
        Claims claims = null;
        try {
			// Get Claims from valid token
			claims = Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody();

		} catch (ExpiredJwtException e) {
			log.info("document.getAuthentication - Expired JWT token.");
			log.trace("document.getAuthentication - Expired JWT token trace: {}", e.getMessage());
			
			claims = e.getClaims();
		}
        Collection<? extends GrantedAuthority> authorities = Arrays
            .stream(claims.get(AUTHORITIES_KEY).toString().split(","))
            .map(SimpleGrantedAuthority::new)
            .collect(Collectors.toList());

        String subject;
        try {
            subject = decrypt(claims.getSubject());
        } catch (Exception e) {
            log.info("Error JWT signature. {}", token);
            throw e;
        }

        User principal = new User(subject, "", authorities);

        return new UsernamePasswordAuthenticationToken(principal, token, authorities);
    }
}