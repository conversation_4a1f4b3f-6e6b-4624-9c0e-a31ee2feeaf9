# Report API Documentation

## Overview

The Report API provides functionality to generate Excel reports of submission data with filtering capabilities. The report includes all submission fields (similar to TaskListDTO but without taskName) along with report metadata.

## Endpoint

### POST /api/staggered/report

Generates an Excel report of submissions based on the provided filters.

#### Request Body

```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "status": "Pending"
}
```

**Parameters:**

- `startDate` (optional): Start date for filtering submissions by creation date (createdAt field)
- `endDate` (optional): End date for filtering submissions by creation date (createdAt field)
- `status` (optional): Status filter for submissions

**Date Format:**

- Accepts ISO date format: `YYYY-MM-DD`
- Accepts ISO datetime format: `YYYY-MM-DDTHH:mm:ss`

#### Response

**Success (200 OK):**

- Returns Excel file as attachment
- Content-Type: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- Content-Disposition: `attachment; filename=submission_report_YYYYMMDD_HHMMSS.xlsx`

**Error Responses:**

**400 Bad Request:**

```json
{
  "code": 400,
  "status": "Bad Request",
  "message": "Invalid date format: invalid-date"
}
```

**401 Unauthorized:**

```json
{
  "code": 401,
  "status": "Authentication Error",
  "message": "User not authenticated"
}
```

**404 Not Found:**

```json
{
  "code": 404,
  "status": "No Data Found",
  "message": "No submissions found matching the specified criteria"
}
```

**500 Internal Server Error:**

```json
{
  "code": 500,
  "status": "Internal Server Error",
  "message": "Failed to generate report Excel: [error details]"
}
```

## Excel Report Structure

The generated Excel file has a professional report format:

### Report Header (Rows 1-2)

- **A1**: "Report Date:" (bold)
- **B1**: Actual report generation date and time
- **A2**: "Generated By:" (bold)
- **B2**: Username of the person who generated the report

### Data Table (Starting from Row 4)

The table has borders and contains the following columns:

**Visual Layout:**

```
Row 1: | Report Date:     | 14-Jul-2024 14:45:30 |                |
Row 2: | Generated By:    | john.doe              |                |
Row 3: |                  |                       |                | (empty row)
Row 4: | ID | Ref Number  | Submitter Name        | Status | ... | (header with borders)
Row 5: | 1  | SP-001      | John Doe              | Pending| ... | (data with borders)
Row 6: | 2  | SP-002      | Jane Smith            | Approved|... | (data with borders)
```

| Column           | Description                                  |
| ---------------- | -------------------------------------------- |
| ID               | Submission ID                                |
| Reference Number | Unique submission reference                  |
| Submitter Name   | Name of the person who submitted             |
| Submitter Job    | Job title of submitter                       |
| Status           | Current submission status                    |
| NIP              | Employee NIP                                 |
| Name             | Employee name                                |
| Grade            | Employee grade                               |
| Payment Type     | Type of payment                              |
| Amount           | Payment amount (formatted as general number) |
| Description      | Payment description                          |
| Month of Process | Processing month                             |
| Year of Process  | Processing year                              |
| Directorate      | Employee directorate                         |
| SLIK             | SLIK information                             |
| Sanction         | Sanction details                             |
| Termination Date | Termination date if applicable               |
| Eligible         | Eligibility status                           |
| Payment Date     | Actual payment date (shows "-" if null)      |
| Remarks          | Additional remarks (shows "-" if null)       |

## Features

1. **Date Filtering**: Filter submissions by creation date range
2. **Status Filtering**: Filter by submission status
3. **Null Handling**: Null values are displayed as "-" for better readability
4. **Report Metadata**: Report date and generated by information in header (not part of table)
5. **Authentication**: Requires authenticated user
6. **Professional Format**:
   - No gridlines for clean appearance
   - Bordered table starting from row 4
   - Bold headers with borders
   - Auto-sized columns
   - Proper number formatting for amounts

## Usage Examples

### Generate report for all submissions in January 2024

```bash
curl -X POST /api/staggered/report \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31"
  }'
```

### Generate report for pending submissions only

```bash
curl -X POST /api/staggered/report \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "status": "Pending"
  }'
```

### Generate report for specific date range and status

```bash
curl -X POST /api/staggered/report \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "status": "Approved"
  }'
```

## Implementation Details

- **Service**: `ReportService`
- **Controller**: `StaggeredPaymentController.generateReport()`
- **Filtering**: Uses `SubmissionFilterService` for date parsing
- **Security**: Uses `SecurityUtil.getCurrentUserLogin()` for authentication
- **Excel Generation**: Uses Apache POI (XSSFWorkbook)
- **Date Filtering**: Filters by `createdAt` field (submission creation date)

## Testing

Comprehensive tests are available:

- **Unit Tests**: `ReportServiceTest.java`
- **Controller Tests**: `StaggeredPaymentControllerTest.ReportTests`

Tests cover:

- Successful report generation
- Error handling (authentication, validation, no data)
- Null value handling
- Date filtering scenarios
